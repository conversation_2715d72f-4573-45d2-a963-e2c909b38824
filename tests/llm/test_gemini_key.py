import os
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables from a .env file (optional, but good practice)
load_dotenv()

# Get the API key from environment variables
# Check both GEMINI_API_KEY and GOOGLE_GEMINI_API_KEY for compatibility
api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_GEMINI_API_KEY")

if not api_key:
    print("Error: GEMINI_API_KEY or GOOGLE_GEMINI_API_KEY not found in environment variables.")
    print("Please set one of them in your .env file or directly in the script.")
else:
    try:
        print(f"Configuring Gemini with API key: {api_key[:4]}...{api_key[-4:]}")
        genai.configure(api_key=api_key)

        print("Attempting to generate text with Gemini...")
        # Make a simple text generation request
        prompt = "What is the capital of France?"

        # Try the newer GenerativeModel approach first (for newer versions)
        try:
            model = genai.GenerativeModel('gemini-pro')
            response = model.generate_content(prompt)

            # Print the response
            print("\nResponse from Gemini (GenerativeModel):")
            if hasattr(response, 'text'):
                print(response.text)
                print("\n✅ Gemini API key seems to be working correctly!")
            elif hasattr(response, 'candidates') and response.candidates:
                # For gemini-1.5-flash, the text is often in parts
                generated_text = "".join(part.text for part in response.candidates[0].content.parts)
                print(generated_text)
                print("\n✅ Gemini API key seems to be working correctly!")
            else:
                print("No candidates found in the response.")
                print(f"Full response object: {response}")

        except AttributeError:
            # Fallback to older generate_text method for v0.1.0rc1
            print("GenerativeModel not available, trying generate_text...")

            # Try different model names that might work with this API key
            model_names = ['gemini-pro', 'text-bison-001', 'models/text-bison-001']
            response = None

            for model_name in model_names:
                try:
                    print(f"Trying model: {model_name}")
                    response = genai.generate_text(
                        model=model_name,
                        prompt=prompt
                    )
                    if response and hasattr(response, 'result') and response.result:
                        print(f"Success with model: {model_name}")
                        break
                except Exception as model_error:
                    print(f"Model {model_name} failed: {model_error}")
                    continue

            print("\nResponse from Gemini (generate_text):")
            if response and hasattr(response, 'result') and response.result:
                print(response.result)
                print("\n✅ Gemini API key seems to be working correctly!")
            else:
                print("No result found in the response.")
                print(f"Full response object: {response}")


    except Exception as e:
        print(f"\n❌ An error occurred: {e}")
        print("This could be due to an invalid API key, network issues, or insufficient permissions/quota.")
        print("Please double-check your API key and ensure the Generative Language API is enabled in your Google Cloud project.")

