api:
  host: 0.0.0.0
  port: 8000

llm:
  provider: groq # Set the default provider to 'groq' since GROQ_API_KEY is available
  models:
    openai:
      api_key: ${OPENAI_API_KEY}
      model_name: "gpt-4"
    groq:
      api_key: ${GROQ_API_KEY} # Use the GROQ_API_KEY from the environment
      model_name: "deepseek-r1-distill-llama-70b"
    openrouter:
      api_key: ${OPENROUTER_API_KEY}
      model_name: "meta-ai/llama-3.1-8b-instruct"
    gemini:
      api_key: ${GOOGLE_GEMINI_API_KEY}
      model_name: "gemini-1.5-flash"

git:
  github_token: ${GITHUB_TOKEN}
  gitlab_token: ${GITLAB_TOKEN}
  temp_dir: ./data/repo

documentation:
  output_dir: ./data/generated-output
  templates_dir: ./docs/templates
