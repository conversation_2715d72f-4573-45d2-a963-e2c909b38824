

import os
import psutil

def find_any_active_venv_low_level():
    """
    Scans running processes by reading /proc/<pid>/environ directly to find
    any terminal session with an active virtual environment.
    This is a more low-level approach than relying on psutil's environ cache.
    """
    print("🔍 Searching for any active Python virtual environments (low-level scan)...\n")

    active_sessions = {}  # PID -> venv_path

    # A list of common shell process names
    shell_names = ["bash", "zsh", "sh", "ksh", "fish"]

    # Iterate over all running processes
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            # Filter for common shell names
            if proc.info.get('name') not in shell_names:
                continue

            pid = proc.info['pid']
            environ_file = f'/proc/{pid}/environ'

            # Check if the environ file exists and is readable
            if not os.path.exists(environ_file):
                continue

            # Read the null-byte separated environment variables
            with open(environ_file, 'rb') as f:
                # Read the whole file, split by null bytes
                env_data = f.read().split(b'\x00')

            # Search for VIRTUAL_ENV in the decoded strings
            for entry_bytes in env_data:
                entry = entry_bytes.decode('utf-8', errors='ignore')
                if entry.startswith("VIRTUAL_ENV="):
                    # Found it, store the PID and the path
                    venv_path = entry.split('=', 1)[1]
                    active_sessions[pid] = venv_path
                    # No need to check other variables for this process
                    break

        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess, FileNotFoundError, PermissionError):
            # Ignore processes that are gone, inaccessible, or we don't have permission for
            continue

    if not active_sessions:
        print("✅ No active virtual environments found in any terminal session.")
        print("   (Note: This script often can't see the session it is running from.)")
    else:
        print(f"✅ Found {len(active_sessions)} terminal session(s) with active virtual environments:")
        for pid, venv_path in active_sessions.items():
            print(f"   - Process ID (PID): {pid}")
            print(f"     -> Environment: {venv_path}")

    print("\n💡 Tip: You can use 'htop -p PID' or 'ps -f -p PID' to inspect a process.")

if __name__ == "__main__":
    find_any_active_venv_low_level()
