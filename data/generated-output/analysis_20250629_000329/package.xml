<?xml version="1.0"?>
# Root element of the package description
<package format="2">
  <name>teb_planner_service</name>
# Package version (currently set to 0.0.0)
  <version>0.0.0</version>
# Brief description of the package
  <description>The teb_planner_service package</description>

  <!-- One maintainer tag required, multiple allowed, one person per tag -->
# Maintainer contact information (email and name)
  <!-- Example:  -->
  <!-- <maintainer email="<EMAIL>"><PERSON></maintainer> -->
  <maintainer email="<EMAIL>">oct-cs-07</maintainer>
# License information for the package (TODO: update)


  <!-- One license tag required, multiple allowed, one license per tag -->
# Build dependency for generating ROS messages
  <!-- Commonly used license strings: -->
  <!--   BSD, MIT, Boost Software License, GPLv2, GPLv3, LGPLv2.1, LGPLv3 -->
  <license>TODO</license>
# Build tool dependency for Catkin

# Build dependency for ROS C++

# Build dependency for ROS Python

# Build dependency for standard ROS messages
    <build_depend>message_generation</build_depend>
# Build dependency for geometry messages
  <!-- Use build_export_depend for packages you need in order to build against this package: -->
# Build dependency for navigation messages
  <!--   <build_export_depend>message_generation</build_export_depend> -->
# Build dependency for costmap conversion
  <!-- Use buildtool_depend for build tool packages: -->
# Build dependency for TEB local planner
  <!--   <buildtool_depend>catkin</buildtool_depend> -->
  <!-- Use exec_depend for packages you need at runtime: -->
    <exec_depend>message_runtime</exec_depend>
# Exported dependencies for ROS C++
  <!-- Use test_depend for packages you need only for testing: -->
# Exported dependencies for ROS Python
  <!--   <test_depend>gtest</test_depend> -->
# Exported dependencies for standard ROS messages
  <!-- Use doc_depend for packages you need only for building documentation: -->
  <!--   <doc_depend>doxygen</doc_depend> -->
# Runtime dependency for ROS C++
  <buildtool_depend>catkin</buildtool_depend>
# Runtime dependency for ROS Python
  <build_depend>roscpp</build_depend>
# Runtime dependency for standard ROS messages
  <build_depend>rospy</build_depend>
# Runtime dependency for geometry messages
  <build_depend>std_msgs</build_depend>
# Runtime dependency for navigation messages
  <build_depend>geometry_msgs</build_depend>
# Runtime dependency for costmap conversion
  <build_depend>nav_msgs</build_depend>
# Runtime dependency for TEB local planner
  <build_depend>costmap_converter</build_depend>
  <build_depend>teb_local_planner</build_depend>

# Export tag for additional information
  <build_export_depend>roscpp</build_export_depend>
  <build_export_depend>rospy</build_export_depend>
  <build_export_depend>std_msgs</build_export_depend>
  <exec_depend>roscpp</exec_depend>
  <exec_depend>rospy</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>nav_msgs</exec_depend>
  <exec_depend>costmap_converter</exec_depend>
  <exec_depend>teb_local_planner</exec_depend>


  <!-- The export tag contains other, unspecified, tags -->
  <export>
    <!-- Other tools can request additional information be placed here -->

  </export>
</package>
