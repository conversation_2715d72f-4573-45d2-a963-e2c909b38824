#include <ros/ros.h>
#include <teb_local_planner/teb_local_planner_ros.h>
#include <visualization_msgs/Marker.h>
#include <boost/shared_ptr.hpp>
#include <boost/make_shared.hpp>
#include <geometry_msgs/PoseStamped.h>
#include <costmap_converter/ObstacleArrayMsg.h>
#include <nav_msgs/Path.h>
#include "teb_planner_service/PlanTrajectory.h"

/** @brief a service server that returns a nav_msgs/Path using teb local planner
 *
 * @return nav_msgs/Path
 */

using namespace teb_local_planner;

PlannerInterfacePtr planner;
/**
 * Global pointer to the TEB planner instance
 */
TebVisualizationPtr visual;
/**
 * Global visualization helper for TEB-related data
 */
std::vector<ObstaclePtr> obst_vector;
/**
 * Global container for obstacle pointers
 */
ViaPointContainer via_points;
/**
 * Global container for via points
 */
TebConfig config;
/**
 * Global TEB configuration parameters
 */
boost::shared_ptr<dynamic_reconfigure::Server<TebLocalPlannerReconfigureConfig>> dynamic_recfg;
/**
 * Global dynamic reconfigure server pointer
 */
ros::Subscriber custom_obst_sub;
/**
 * Global subscriber for custom obstacles
 */
ros::ServiceServer teb_service;
/**
 * Global service server for trajectory planning requests
 */

void CB_publishCycle(const ros::TimerEvent &e);
void CB_reconfigure(TebLocalPlannerReconfigureConfig &reconfig, uint32_t level);
bool planTrajectory(teb_planner_service::PlanTrajectory::Request &req, teb_planner_service::PlanTrajectory::Response &res);
/**
 * Initialize ROS node and setup private node handle
 */

int main(int argc, char **argv)
/**
 * Load TEB configuration parameters from ROS parameter server
 */
{
    ros::init(argc, argv, "test_teb_service");
/**
 * Setup dynamic reconfigure server for runtime parameter tuning
 */
    ros::NodeHandle n("~");

/**
 * Initialize visualization helper with ROS node handle and config
 */
    config.loadRosParamFromNodeHandle(n);

/**
 * Determine robot shape from ROS parameters
 */
    // Setup dynamic reconfigure
    dynamic_recfg = boost::make_shared<dynamic_reconfigure::Server<TebLocalPlannerReconfigureConfig>>(n);
/**
 * Advertise service for trajectory planning requests
 */
    dynamic_reconfigure::Server<TebLocalPlannerReconfigureConfig>::CallbackType cb = boost::bind(CB_reconfigure, _1, _2);
    dynamic_recfg->setCallback(cb);

    // Setup visualization
    visual = TebVisualizationPtr(new TebVisualization(n, config));

/**
 * Service callback function to compute and return a trajectory plan
 */
    // Setup robot shape model
    config.robot_model = TebLocalPlannerROS::getRobotFootprintFromParamServer(n, config);

    // std::cout<<"service is ready !!"<<std::endl;
/**
 * Convert obstacles from request message to TEB internal format
 */
    teb_service = n.advertiseService("plan_trajectory", planTrajectory);
    ROS_INFO("TEB Planner Service Ready!");

    // ros::Timer publish_timer = n.createTimer(ros::Duration(0.1), CB_publishCycle);

    ros::spin();
    return 0;
}
/**
 * Select appropriate planner based on homotopy class planning setting
 */

bool planTrajectory(teb_planner_service::PlanTrajectory::Request &req,
                    teb_planner_service::PlanTrajectory::Response &res)
{
    ROS_INFO("Received request for trajectory planning.");
/**
 * Convert start pose from request to TEB internal SE2 format
 */

/**
 * Convert goal pose from request to TEB internal SE2 format
 */
    // Convert obstacles from message to TEB format
    obst_vector.clear();
/**
 * Compute the optimal trajectory plan
 */
    for (const auto &obst : req.obstacles.obstacles)
    {
        if (obst.polygon.points.size() == 1)
        {
            obst_vector.push_back(ObstaclePtr(new PointObstacle(obst.polygon.points[0].x, obst.polygon.points[0].y)));
        }
/**
 * Retrieve the optimal trajectory from the planner
 */
        else
        {
            PolygonObstacle *poly_obst = new PolygonObstacle;
            for (const auto &point : obst.polygon.points)
            {
/**
 * Construct the response path message
 */
                poly_obst->pushBackVertex(point.x, point.y);
            }
            poly_obst->finalizePolygon();
/**
 * Convert planned poses to ROS path message format
 */
            obst_vector.push_back(ObstaclePtr(poly_obst));
        }
    }

    // Select planner type
    if (config.hcp.enable_homotopy_class_planning)
        planner = PlannerInterfacePtr(new HomotopyClassPlanner(config, &obst_vector, visual, &via_points));
    else
        planner = PlannerInterfacePtr(new TebOptimalPlanner(config, &obst_vector, visual, &via_points));

    // Convert current pose and goal pose to PoseSE2
/**
 * Periodic callback for publishing visualization data
 */
    PoseSE2 start(req.current_pose.pose.position.x, req.current_pose.pose.position.y, tf::getYaw(req.current_pose.pose.orientation));
    PoseSE2 goal(req.goal_pose.pose.position.x, req.goal_pose.pose.position.y, tf::getYaw(req.goal_pose.pose.orientation));

/**
 * Dynamic reconfigure callback to update planner configuration
 */
    // Plan trajectory
    if (!planner->plan(start, goal))
    {
        ROS_WARN("TEB Planner failed to compute a trajectory!");
        return false;
    }

    // Get best trajectory
    TebOptimalPlannerPtr planner_optimal = boost::dynamic_pointer_cast<TebOptimalPlanner>(planner);
    if (!planner_optimal)
    {
        HomotopyClassPlannerPtr planner_homotopy = boost::dynamic_pointer_cast<HomotopyClassPlanner>(planner);
        if (planner_homotopy)
            planner_optimal = planner_homotopy->bestTeb();
    }

    if (!planner_optimal)
    {
        ROS_ERROR("Failed to retrieve the optimal trajectory from the planner!");
        return false;
    }

    nav_msgs::Path teb_path;
    teb_path.header.frame_id = config.map_frame;
    teb_path.header.stamp = ros::Time::now();

    for (int i = 0; i < planner_optimal->teb().sizePoses(); i++)
    {
        geometry_msgs::PoseStamped pose;
        pose.header = teb_path.header;
        pose.pose.position.x = planner_optimal->teb().Pose(i).x();
        pose.pose.position.y = planner_optimal->teb().Pose(i).y();
        pose.pose.position.z = config.hcp.visualize_with_time_as_z_axis_scale * planner_optimal->teb().getSumOfTimeDiffsUpToIdx(i);
        pose.pose.orientation = tf::createQuaternionMsgFromYaw(planner_optimal->teb().Pose(i).theta());
        teb_path.poses.push_back(pose);
    }

    res.trajectory = teb_path;
    ROS_INFO("Trajectory successfully planned and sent.");
    return true;
}

/** @brief for visualization
 *
 */

void CB_publishCycle(const ros::TimerEvent &e)
{
    if (planner)
    {
        planner->visualize();
        visual->publishObstacles(obst_vector);
        visual->publishViaPoints(via_points);
    }
}

void CB_reconfigure(TebLocalPlannerReconfigureConfig &reconfig, uint32_t level)
{
    config.reconfigure(reconfig);
}
