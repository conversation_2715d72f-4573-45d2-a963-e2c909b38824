
# To use:
#
#     pre-commit run -a
#
# Or:
#
#     pre-commit install  # (runs every time you commit in git)
#
# To update this file:
#
#     pre-commit autoupdate
# Excludes third-party or generated code paths from linting/formatting to avoid conflicts or unnecessary checks.
#
# See https://github.com/pre-commit/pre-commit

exclude: ^3rdparty/|3rdparty|^include/behaviortree_cpp/contrib/
repos:
# Pins the hook repository version for reproducibility and to ensure consistent behavior across different developer environments.

  # Standard hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: check-added-large-files
      - id: check-ast
      - id: check-case-conflict
      - id: check-docstring-first
      - id: check-merge-conflict
      - id: check-symlinks
# Prevents accidental commits of common debugging statements (e.g., pdb, breakpoint) into the codebase.
      - id: check-xml
      - id: check-yaml
# Excludes SVG files from newline/whitespace checks, as these can be sensitive to formatting changes or generated by tools.
      - id: debug-statements
      - id: end-of-file-fixer
        exclude_types: [svg]
      - id: mixed-line-ending
      - id: trailing-whitespace
        exclude_types: [svg]
      - id: fix-byte-order-marker

# Pins the hook repository version for reproducibility and to ensure consistent behavior across different developer environments.
  # CPP hooks
  - repo: https://github.com/pre-commit/mirrors-clang-format
    rev: v17.0.6
    hooks:
# Uses '-fallback-style=none' to ensure clang-format only applies the project's defined style, preventing default style application if no config is found.
      - id: clang-format
        args: ['-fallback-style=none', '-i']
