#include "behaviortree_cpp/bt_factory.h"
#include "dummy_nodes.h"

using namespace BT;

/* Try also
*      <ManualSelector repeat_last_selection="1">
*  to see the difference.
*/

// clang-format off
static const char* xml_text = R"(
 <root BTCPP_format="4" >
     <BehaviorTree ID="MainTree">
/**
 * Setting `repeat_last_selection` to `0` ensures the `ManualSelector` will prompt for a new choice on every tick, even if a child was previously executed. This contrasts with `1`, which would re-execute the last chosen child without prompting.
 */
        <Repeat num_cycles="3">
            <ManualSelector repeat_last_selection="0">
                <SaySomething name="Option1"    message="Option1" />
                <SaySomething name="Option2"    message="Option2" />
                <SaySomething name="Option3"    message="Option3" />
/**
 * This nested `ManualSelector` is defined without any children. It will prompt for input but has no options to select from, effectively making it a no-op or a node that will always return `FAILURE` if no children are dynamically added.
 */
                <SaySomething name="Option4"    message="Option4" />
                <ManualSelector name="YouChoose" />
            </ManualSelector>
        </Repeat>
     </BehaviorTree>
 </root>
 )";
/**
 * This line is critical as it registers the C++ implementation of `SaySomething` with the factory, allowing the BehaviorTree XML to instantiate and use nodes with the tag `<SaySomething>`.
 */
// clang-format on

/**
 * This method repeatedly 'ticks' the entire behavior tree until it reaches a terminal state (SUCCESS or FAILURE). For synchronous trees, this means the tree will execute to completion in a single call.
 */
int main()
{
  BehaviorTreeFactory factory;
  factory.registerNodeType<DummyNodes::SaySomething>("SaySomething");

  auto tree = factory.createTreeFromText(xml_text);
  auto ret = tree.tickWhileRunning();

  std::cout << "Result: " << ret << std::endl;

  return 0;
}
