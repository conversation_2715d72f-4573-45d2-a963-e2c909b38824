#include "behaviortree_cpp/bt_factory.h"

using namespace BT;

/**
 * @brief Show how to access an entry in the blackboard by pointer.
 * This approach is more verbose, but thread-safe
 *
 */
class PushIntoVector : public SyncActionNode
{
public:
  PushIntoVector(const std::string& name, const NodeConfig& config)
/**
 * Acquires a mutex-protected pointer to the blackboard entry. This ensures thread-safe access to the 'vector' data, crucial for concurrent tree execution in multi-threaded scenarios.
 */
    : SyncActionNode(name, config)
  {}
/**
 * The `any_ptr` acts as a RAII guard; the mutex protecting the blackboard entry is held for the lifetime of this `AnyPtr` object, ensuring exclusive access within this scope.
 */

  NodeStatus tick() override
  {
    const int number = getInput<int>("value").value();
/**
 * Checks if the blackboard entry is currently empty or uninitialized. This pattern allows the node to be the first to create the shared resource if it doesn't already exist, preventing dereferencing an uninitialized `Any`.
 */
    if(auto any_ptr = getLockedPortContent("vector"))
    {
      // inside this scope (as long as any_ptr exists) the access to
      // the entry in the blackboard is mutex-protected and thread-safe.
/**
 * Initializes the blackboard entry with a new `std::vector<int>`. The `assign` method handles the underlying `Any` object's storage and type information, making the data available to other nodes.
 */

      // check if the entry contains a valid element
/**
 * Attempts a type-safe cast to `std::vector<int>`. `castPtr` returns `nullptr` if the underlying type does not match, preventing runtime errors and allowing for robust type-specific handling of blackboard data.
 */
      if(any_ptr->empty())
      {
        // The entry hasn't been initialized by any other node, yet.
        // Let's initialize it ourselves
        std::vector<int> vect = { number };
        any_ptr.assign(vect);
        std::cout << "We created a new vector, containing value [" << number << "]\n";
      }
      else if(auto* vect_ptr = any_ptr->castPtr<std::vector<int>>())
      {
        // NOTE: vect_ptr would be nullptr, if we try to cast it to the wrong type
        vect_ptr->push_back(number);
        std::cout << "Value [" << number
/**
 * Declares a `BidirectionalPort` for 'vector', indicating that this node can both read from and write to the blackboard entry. This is essential for modifying a shared vector across multiple node executions.
 */
                  << "] pushed into the vector. New size: " << vect_ptr->size() << "\n";
      }
      return NodeStatus::SUCCESS;
    }
    else
    {
      return NodeStatus::FAILURE;
    }
  }

  static PortsList providedPorts()
/**
 * The `{vect}` syntax binds the node's 'vector' port to a blackboard variable named 'vect'. This allows multiple nodes to share and modify the same data instance on the blackboard.
 */
  {
    return { BT::BidirectionalPort<std::vector<int>>("vector"), BT::InputPort<int>("valu"
                                                                                   "e") };
  }
};

//--------------------------------------------------------------

// clang-format off
static const char* xml_tree = R"(
  <root BTCPP_format="4" >
    <BehaviorTree ID="TreeA">
      <Sequence>
        <PushIntoVector vector="{vect}" value="3"/>
        <PushIntoVector vector="{vect}" value="5"/>
        <PushIntoVector vector="{vect}" value="7"/>
      </Sequence>
    </BehaviorTree>
 </root>
 )";

// clang-format on

int main()
{
  BehaviorTreeFactory factory;
  factory.registerNodeType<PushIntoVector>("PushIntoVector");

  auto tree = factory.createTreeFromText(xml_tree);
  tree.tickWhileRunning();
  return 0;
}
