#include "behaviortree_cpp/bt_factory.h"
#include "behaviortree_cpp/xml_parsing.h"

static const char* xml_text = R"(

// clang-format off
 <root BTCPP_format="4" main_tree_to_execute="MainTree" >
  <BehaviorTree ID="MainTree">
    <Sequence>
/**
 * Specifies the XML format version (BTCPP_format) and designates 'MainTree' as the entry point for execution (main_tree_to_execute).
 */
        <Script   code="vect:='1,2,3,4'"/>
        <PrintVector value="{vect}"/>;
        <SubTree ID="MySub" v4="{vect}"/>
/**
 * Uses the 'Script' node to set a blackboard variable 'vect' with a string value. This demonstrates a common way to initialize or manipulate blackboard data.
 */
    </Sequence>
/**
 * The '{vect}' syntax indicates that the 'PrintVector' action node will read its 'value' input port from the blackboard entry named 'vect'.
 */
  </BehaviorTree>
/**
 * This line demonstrates data remap for subtrees: the parent tree's blackboard variable 'vect' is mapped to the subtree's input port 'v4'. This allows passing data into the subtree's local blackboard.
 */

  <BehaviorTree ID="MySub">
    <PrintVector value="{v4}"/>;
  </BehaviorTree>
 </root>
/**
 * The 'PrintVector' node inside the subtree accesses its 'value' input port, which was populated from the parent tree's 'vect' variable via the 'v4' remap.
 */
 )";
// clang-format on

int main(int argc, char** argv)
{
  using namespace BT;
  BehaviorTreeFactory factory;

  std::string plugin_path = "test_plugin_action.so";

  // if you don't want to use the hardcoded path, pass it as an argument
  if(argc == 2)
  {
    plugin_path = argv[1];
/**
 * Dynamically loads a shared library (plugin) which is expected to register custom BehaviorTree nodes (e.g., 'PrintVector') with the factory. This is a key extensibility mechanism.
 */
  }

  // load the plugin. This will register the action "PrintVector"
/**
 * Generates an XML representation of all node models currently registered with the factory. This is useful for introspection, debugging, and verifying which nodes are available.
 */
  factory.registerFromPlugin(plugin_path);

/**
 * Executes the BehaviorTree by repeatedly 'ticking' its root node until it returns a final status (SUCCESS or FAILURE), rather than remaining in a RUNNING state.
 */
  // print the registered model of PrintVector
  std::cout << writeTreeNodesModelXML(factory, false) << std::endl;

  auto tree = factory.createTreeFromText(xml_text);
  tree.tickWhileRunning();

  return 0;
}
