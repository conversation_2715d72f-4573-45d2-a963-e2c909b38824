#include "custom_type.hpp"
#include "behaviortree_cpp/bt_factory.h"

class PrintVector : public BT::SyncActionNode
{
/**
 * Inherits from `SyncActionNode` to indicate a synchronous, non-blocking action that completes its execution within a single `tick()` cycle.
 */
public:
  PrintVector(const std::string& name, const BT::NodeConfig& config)
    : BT::SyncActionNode(name, config)
  {}

  BT::NodeStatus tick() override
  {
/**
 * Retrieves the `Vector4D` value from the 'value' input port. The use of `.value()` assumes the input is mandatory and always present, bypassing explicit `std::optional` checks for brevity.
 */
    const auto v = getInput<Vector4D>("value").value();
    printf("x:%f  y:%f  z:%f  w:%f\n", v.x, v.y, v.z, v.w);
    return BT::NodeStatus::SUCCESS;
  }

/**
 * Mandatory static method required by BehaviorTree.CPP to declare the node's input and output ports, enabling the factory to understand its interface.
 */
  // It is mandatory to define this static method.
  static BT::PortsList providedPorts()
/**
 * Declares an input port named 'value' of type `Vector4D`. This allows the BehaviorTree engine to validate connections and provide type safety.
 */
  {
    return { BT::InputPort<Vector4D>("value") };
  }
};

// Function used to register PrintVector automatically, when
// loading the plugin.
// Remember that it is mandatory to add to the CMakeLists.txt file this:
//
//    target_compile_definitions(<target_name> PRIVATE  BT_PLUGIN_EXPORT)
/**
 * Utilizes a BehaviorTree.CPP macro for automatic node registration. This is crucial for dynamically loading nodes as plugins without explicit factory calls in the main application.
 */
//
/**
 * Registers the `PrintVector` C++ class with the BehaviorTree factory under the string alias 'PrintVector', making it available for use in XML behavior trees.
 */
BT_REGISTER_NODES(factory)
{
  factory.registerNodeType<PrintVector>("PrintVector");
}
