#include "behaviortree_cpp/bt_factory.h"

//#define MANUAL_STATIC_LINKING
/**
 * This preprocessor directive controls the node registration strategy, allowing either static linking (direct code inclusion) or dynamic loading (plugin). This is a key technical decision for deployment flexibility.
 */

#ifdef MANUAL_STATIC_LINKING
#include "dummy_nodes.h"
#endif

using namespace BT;

/** Behavior Tree are used to create a logic to decide what
 * to "do" and when. For this reason, our main building blocks are
 * Actions and Conditions.
 *
 * In this tutorial, we will learn how to create custom ActionNodes.
 * It is important to remember that NodeTree are just a way to
 * invoke callbacks (called tick() ). These callbacks are implemented by the user.
 */
/**
 * Defines the Behavior Tree structure using an XML string literal. This is the declarative way to define the tree's topology and node instances.
 */

// clang-format off
static const char* xml_text = R"(

 <root BTCPP_format="4" >

     <BehaviorTree ID="MainTree">
        <Sequence name="root_sequence">
            <CheckBattery   name="battery_ok"/>
            <OpenGripper    name="open_gripper"/>
            <ApproachObject name="approach_object"/>
            <CloseGripper   name="close_gripper"/>
        </Sequence>
     </BehaviorTree>

 </root>
 )";

// clang-format on

int main()
/**
 * Registers a custom C++ class (`ApproachObject`) as a Behavior Tree node. This is the recommended approach for complex nodes that require state, ports, or more intricate logic, leveraging C++ inheritance.
 */
{
  // We use the BehaviorTreeFactory to register our custom nodes
  BehaviorTreeFactory factory;

/**
 * Registers a simple condition node using a C++11 lambda. This method is suitable for stateless, straightforward conditions that can be represented by a single function call.
 */
  /* There are two ways to register nodes:
    *    - statically, i.e. registering all the nodes one by one.
    *    - dynamically, loading the TreeNodes from a shared library (plugin).
    * */
/**
 * Registers a simple action node by binding it to a method of an existing object (`gripper`). This demonstrates how to integrate class methods as BT actions, allowing the BT to interact with external C++ objects.
 */

#ifdef MANUAL_STATIC_LINKING
  // Note: the name used to register should be the same used in the XML.
  // Note that the same operations could be done using DummyNodes::RegisterNodes(factory)

  using namespace DummyNodes;
/**
 * Dynamically loads and registers all Behavior Tree nodes contained within a shared library (plugin). This approach promotes modularity and allows for extending the BT system without recompiling the main application.
 */

  // The recommended way to create a Node is through inheritance.
  // Even if it requires more boilerplate, it allows you to use more functionalities
  // like ports (we will discuss this in future tutorials).
  factory.registerNodeType<ApproachObject>("ApproachObject");

/**
 * Parses the XML definition and instantiates the Behavior Tree. The `tree` object manages the lifecycle of all created nodes, ensuring they are properly destroyed when `tree` goes out of scope.
 */
  // Registering a SimpleActionNode using a function pointer.
  // you may also use C++11 lambdas instead of std::bind
  factory.registerSimpleCondition("CheckBattery",
                                  [&](TreeNode&) { return CheckBattery(); });
/**
 * Initiates the execution of the Behavior Tree. This method repeatedly 'ticks' the root node until the tree's state is `SUCCESS`, `FAILURE`, or `HALTED`, driving the tree's decision-making process.
 */

  //You can also create SimpleActionNodes using methods of a class
  GripperInterface gripper;
  factory.registerSimpleAction("OpenGripper", [&](TreeNode&) { return gripper.open(); });
  factory.registerSimpleAction("CloseGripper",
                               [&](TreeNode&) { return gripper.close(); });

#else
  // Load dynamically a plugin and register the TreeNodes it contains
  // it automated the registering step.
  factory.registerFromPlugin("../sample_nodes/bin/libdummy_nodes_dyn.so");
#endif

  // Trees are created at deployment-time (i.e. at run-time, but only once at the beginning).
  // The currently supported format is XML.
  // IMPORTANT: when the object "tree" goes out of scope, all the TreeNodes are destroyed
  auto tree = factory.createTreeFromText(xml_text);

  // To "execute" a Tree you need to "tick" it.
  // The tick is propagated to the children based on the logic of the tree.
  // In this case, the entire sequence is executed, because all the children
  // of the Sequence return SUCCESS.
  tree.tickWhileRunning();

  return 0;
}

/* Expected output:
*
       [ Battery: OK ]
       GripperInterface::open
       ApproachObject: approach_object
       GripperInterface::close
*/
