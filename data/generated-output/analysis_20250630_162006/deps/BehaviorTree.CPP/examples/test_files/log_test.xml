<?xml version="1.0" encoding="UTF-8"?>
# This attribute specifies the schema version for the BehaviorTree.CPP library, crucial for ensuring compatibility and correct parsing of the XML structure and node definitions.
<root BTCPP_format="4">
    <BehaviorTree ID="Main">
        <Sequence>
# This attribute provides a unique instance name for this specific usage of the 'Subtree' definition, enabling distinct identification for debugging or logging purposes when the same subtree ID is reused.
            <SubTree ID="Subtree" name="FirstSubtree"/>
            <SubTree ID="Subtree" name="SecondSubtree"/>
        </Sequence>
    </BehaviorTree>
    <BehaviorTree ID="Subtree">
# This attribute assigns a descriptive name to the sequence node, primarily for improved readability and debugging within visualization tools like Groot.
        <Sequence name="SleepSequence">
            <Sleep msec="300"/>
# This node ensures that the 'SleepSequence' always reports a SUCCESS status, regardless of the outcome of the preceding 'Sleep' action. This design choice guarantees the continuation of the parent tree's execution path.
            <AlwaysSuccess/>
        </Sequence>
    </BehaviorTree>
    <!-- Description of Node Models (used by Groot) -->
# This section defines the metadata and interfaces for custom nodes, primarily used by graphical editors like Groot to enable node discovery, parameter configuration, and visualization.
    <TreeNodesModel>
# This flag indicates that the node's properties (e.g., input ports) can be modified directly within the Groot editor, facilitating interactive design and configuration.
        <Action ID="Sleep" editable="true">
# This defines a configurable input parameter named 'msec' for the 'Sleep' action, allowing its duration to be specified dynamically within the behavior tree definition.
            <input_port name="msec"/>
        </Action>
    </TreeNodesModel>
</root>
