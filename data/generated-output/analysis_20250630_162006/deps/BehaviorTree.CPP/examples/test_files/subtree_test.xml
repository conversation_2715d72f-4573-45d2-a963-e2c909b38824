# This attribute specifies the expected XML schema version for the BehaviorTree.CPP library, crucial for ensuring forward and backward compatibility during parsing and execution of the tree definition.
<root BTCPP_format="4">

# This directive enables modularity by allowing the definition of custom nodes or sub-trees in external XML files. This promotes reusability and simplifies the management of complex behavior tree structures.
    <include path="Check.xml" />
# Similar to the previous include, this demonstrates the use of relative paths for organizing included XML definitions, further supporting a structured and modular design approach for large-scale behavior trees.
    <include path="subtrees/Talk.xml" />

    <BehaviorTree ID="BehaviorTree">
        <Sequence>
            <CheckStatus/>
            <OpenGripper/>
            <CloseGripper/>
            <SayStuff/>
        </Sequence>
    </BehaviorTree>

</root>
