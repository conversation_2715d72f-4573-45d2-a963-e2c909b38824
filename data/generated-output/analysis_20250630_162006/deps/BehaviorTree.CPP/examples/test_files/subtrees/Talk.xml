# Specifies the BehaviorTree.CPP XML format version. This is a critical technical decision to ensure compatibility with the parser and correct interpretation of the tree structure and node definitions.
<root BTCPP_format="4">

    <BehaviorTree ID="SayStuff">
# Utilizes `SequenceWithMemory` to enable stateful execution. This design choice allows the sequence to resume from the last running child, preserving progress across ticks, unlike a standard `Sequence` which restarts from the beginning.
        <SequenceWithMemory>
            <Action ID="SaySomething" message="Hello World"/>
            <Action ID="SayHello"/>
        </SequenceWithMemory>
    </BehaviorTree>

</root>
