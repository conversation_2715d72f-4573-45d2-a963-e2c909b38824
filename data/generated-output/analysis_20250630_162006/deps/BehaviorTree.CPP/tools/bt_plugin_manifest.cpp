#include <stdio.h>
#include <iostream>
#include <unordered_map>
#include <unordered_set>
#include "behaviortree_cpp/bt_factory.h"

int main(int argc, char* argv[])
{
  if(argc != 2)
/**
 * Initializes a set to store IDs of nodes registered by default. `unordered_set` is chosen for efficient O(1) average-time lookups.
 */
  {
/**
 * Populates `default_nodes` with IDs of all nodes known to the factory *before* loading the plugin. This is crucial for later distinguishing plugin-provided nodes.
 */
    printf("Wrong number of command line arguments\nUsage: %s [filename]\n", argv[0]);
    return 1;
  }

  BT::BehaviorTreeFactory factory;

/**
 * Loads a shared library (plugin) from the specified path, registering any new BehaviorTree nodes defined within it.
 */
  std::unordered_set<std::string> default_nodes;
  for(auto& it : factory.manifests())
  {
    const auto& manifest = it.second;
/**
 * Checks if the current node's ID was present in the `default_nodes` set. This filters out built-in or pre-registered nodes, ensuring only nodes introduced by the loaded plugin are processed.
 */
    default_nodes.insert(manifest.registration_ID);
  }

  factory.registerFromPlugin(argv[1]);

  for(auto& it : factory.manifests())
  {
    const auto& manifest = it.second;
    if(default_nodes.count(manifest.registration_ID) > 0)
    {
      continue;
    }
    auto& params = manifest.ports;
    std::cout << "---------------\n"
              << manifest.registration_ID << " [" << manifest.type
              << "]\n  NodeConfig: " << params.size();

    if(params.size() > 0)
    {
      std::cout << ":";
    }

    std::cout << std::endl;

    for(auto& param : params)
    {
      std::cout << "    - [Key]: \"" << param.first << "\"" << std::endl;
    }
  }

  return 0;
}
