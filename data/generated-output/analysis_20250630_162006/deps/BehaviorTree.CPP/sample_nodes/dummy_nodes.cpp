#include "dummy_nodes.h"

// This function must be implemented in the .cpp file to create
// a plugin that can be loaded at run-time
/**
 * This macro is crucial for the BehaviorTree.CPP plugin system. It defines a function that the `BehaviorTreeFactory` will call to register custom nodes when this shared library (plugin) is loaded. This allows for dynamic loading of behavior tree nodes from external files.
 */
BT_REGISTER_NODES(factory)
{
/**
 * This function (presumably defined in `dummy_nodes.h` or another linked file) is responsible for adding all the custom `DummyNodes` (like `CheckBattery`, `SayHello`, `ApproachObject`, etc.) to the `BehaviorTreeFactory`. This makes them available for use in behavior tree XML definitions.
 */
  DummyNodes::RegisterNodes(factory);
}

namespace DummyNodes
{

BT::NodeStatus CheckBattery()
{
  std::cout << "[ Battery: OK ]" << std::endl;
  return BT::NodeStatus::SUCCESS;
}

BT::NodeStatus CheckTemperature()
{
  std::cout << "[ Temperature: OK ]" << std::endl;
  return BT::NodeStatus::SUCCESS;
}

BT::NodeStatus SayHello()
{
  std::cout << "Robot says: Hello World" << std::endl;
  return BT::NodeStatus::SUCCESS;
}

BT::NodeStatus GripperInterface::open()
{
  _opened = true;
  std::cout << "GripperInterface::open" << std::endl;
  return BT::NodeStatus::SUCCESS;
}

BT::NodeStatus GripperInterface::close()
{
  std::cout << "GripperInterface::close" << std::endl;
  _opened = false;
  return BT::NodeStatus::SUCCESS;
}

/**
 * This line demonstrates how to retrieve data from an 'input port' defined for this custom node. Input ports allow behavior tree XML definitions to pass parameters to the node, making it reusable and configurable.
 */
BT::NodeStatus ApproachObject::tick()
/**
 * This checks if the input port `message` was successfully retrieved and contains a value. It's crucial for robust node design to validate required inputs, preventing runtime errors due to misconfigured trees.
 */
{
  std::cout << "ApproachObject: " << this->name() << std::endl;
/**
 * If the required input `message` is not provided or is of the wrong type, a `BT::RuntimeError` is thrown. This ensures that misconfigured behavior trees fail fast and provide clear diagnostic information, aiding debugging.
 */
  return BT::NodeStatus::SUCCESS;
}

BT::NodeStatus SaySomething::tick()
{
  auto msg = getInput<std::string>("message");
  if(!msg)
  {
    throw BT::RuntimeError("missing required input [message]: ", msg.error());
/**
 * This function demonstrates an alternative way to define a custom node in BehaviorTree.CPP, known as a 'SimpleActionNode' or 'SimpleConditionNode'. Instead of inheriting from a base class, the node's logic is implemented as a free function that receives a reference to its `TreeNode` instance, allowing access to inputs/outputs. This is often preferred for simpler, stateless nodes.
 */
  }

/**
 * Similar to the class-based node, this retrieves the input port `message`. The `self` parameter provides access to the `TreeNode`'s interface, including input/output ports, allowing the free function to interact with the behavior tree context.
 */
  std::cout << "Robot says: " << msg.value() << std::endl;
/**
 * Again, robust error handling for missing required inputs is demonstrated. This pattern is essential for creating reliable and maintainable behavior trees, as it clearly signals configuration issues.
 */
  return BT::NodeStatus::SUCCESS;
}

BT::NodeStatus SaySomethingSimple(BT::TreeNode& self)
{
  auto msg = self.getInput<std::string>("message");
  if(!msg)
  {
    throw BT::RuntimeError("missing required input [message]: ", msg.error());
  }

  std::cout << "Robot says: " << msg.value() << std::endl;
  return BT::NodeStatus::SUCCESS;
}

}  // namespace DummyNodes
