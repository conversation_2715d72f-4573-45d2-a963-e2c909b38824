#include "movebase_node.h"
#include "behaviortree_cpp/bt_factory.h"

// This function must be implemented in the .cpp file to create
// a plugin that can be loaded at run-time
BT_REGISTER_NODES(factory)
/**
 * This macro is essential for the BehaviorTree.CPP library's plugin system, allowing this custom node to be dynamically loaded and instantiated by the BT factory. This is a core architectural decision for extensibility.
 */
{
  factory.registerNodeType<MoveBaseAction>("MoveBase");
/**
 * This line explicitly registers the `MoveBaseAction` class with the BehaviorTree factory under the name 'MoveBase'. This mapping is crucial for XML-defined behavior trees to reference and create instances of this C++ action node.
 */
}

BT::NodeStatus MoveBaseAction::onStart()
{
  if(!getInput<Pose2D>("goal", _goal))
/**
 * This demonstrates the standard way to retrieve input ports defined for a BehaviorTree node. The `throw BT::RuntimeError` ensures that the node fails fast if a mandatory input is not provided, preventing undefined behavior or incorrect execution.
 */
  {
    throw BT::RuntimeError("missing required input [goal]");
  }
  printf("[ MoveBase: SEND REQUEST ]. goal: x=%.1f y=%.1f theta=%.1f\n", _goal.x, _goal.y,
         _goal.theta);

/**
 * This line simulates an asynchronous operation with a fixed duration. In a real-world scenario, this would typically involve sending a request to an external system (e.g., a robot's navigation stack) and waiting for a response, rather than a simple time delay.
 */
  // We use this counter to simulate an action that takes a certain
  // amount of time to be completed (220 ms)
  _completion_time = chr::system_clock::now() + chr::milliseconds(220);

  return BT::NodeStatus::RUNNING;
}

BT::NodeStatus MoveBaseAction::onRunning()
/**
 * This `sleep_for` call is a *simulation* of checking for an asynchronous reply. In a production system, this would be replaced by a non-blocking check (e.g., polling a message queue, checking a future's status, or using an event-driven callback) to avoid blocking the main Behavior Tree tick loop, which is critical for responsiveness.
 */
{
  // Pretend that we are checking if the reply has been received
  // you don't want to block inside this function too much time.
  std::this_thread::sleep_for(chr::milliseconds(10));
/**
 * This condition checks if the simulated asynchronous operation has completed its predefined duration. It's the core logic for transitioning the node's state from `RUNNING` to `SUCCESS`.
 */

  // Pretend that, after a certain amount of time,
  // we have completed the operation
  if(chr::system_clock::now() >= _completion_time)
  {
    std::cout << "[ MoveBase: FINISHED ]" << std::endl;
    return BT::NodeStatus::SUCCESS;
  }
  return BT::NodeStatus::RUNNING;
}

void MoveBaseAction::onHalted()
{
  printf("[ MoveBase: ABORTED ]");
}
