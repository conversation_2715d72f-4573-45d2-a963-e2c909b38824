#include <gtest/gtest.h>
#include "action_test_node.h"
#include "condition_test_node.h"
#include "behaviortree_cpp/behavior_tree.h"
#include "behaviortree_cpp/tree_node.h"
#include "behaviortree_cpp/bt_factory.h"

using BT::NodeStatus;
using std::chrono::milliseconds;
/**
 * Defines the BehaviorTree structure in XML. This tree uses a 'Switch3' node, implying it expects 3 explicit cases and a default child. However, the C++ test fixture will instantiate a 'SwitchNode<2>', which will make the third child the default one.
 */

static const char* xml_text = R"(

<root BTCPP_format="4" >

    <BehaviorTree ID="MainTree">
        <Switch3 name="simple_switch" variable="{my_var}"  case_1="1" case_2="42 case_3="666" >
            <AsyncActionTest name="action_1"/>
            <AsyncActionTest name="action_42"/>
            <AsyncActionTest name="action_666"/>
            <AsyncActionTest name="action_default"/>
        </Switch3>
    </BehaviorTree>
</root>
        )";
/**
 * Helper function to repeatedly tick a node until it no longer returns RUNNING. This is essential for testing asynchronous nodes, ensuring they complete their execution or reach a final state (SUCCESS/FAILURE).
 */

BT::NodeStatus TickWhileRunning(BT::TreeNode& node)
{
  auto status = node.executeTick();
  while(status == BT::NodeStatus::RUNNING)
  {
    status = node.executeTick();
  }
/**
 * Defines a Google Test fixture for SwitchNode. The template parameter <2> indicates this SwitchNode is configured to handle 2 explicit cases (case_1, case_2) and one implicit default case (the third child added).
 */
  return status;
}

struct SwitchTest : testing::Test
{
  using Switch2 = BT::SwitchNode<2>;
  std::unique_ptr<Switch2> root;
  BT::AsyncActionTest action_1;
  BT::AsyncActionTest action_42;
/**
 * Initializes the AsyncActionTest nodes. These nodes simulate actions that take a specified duration (200ms) to complete, allowing for testing of asynchronous behavior and state transitions.
 */
  BT::AsyncActionTest action_def;
  BT::Blackboard::Ptr bb = BT::Blackboard::create();
  BT::NodeConfig simple_switch_config_;
/**
 * Configures the input ports for the SwitchNode. 'variable' links to the blackboard key 'my_var'. 'case_1' and 'case_2' define the values that will trigger the first and second child respectively. Note that 'case_3' from the XML is not defined here, reinforcing that this is a SwitchNode<2>.
 */

  SwitchTest()
    : action_1("action_1", milliseconds(200))
    , action_42("action_42", milliseconds(200))
    , action_def("action_default", milliseconds(200))
  {
    BT::PortsRemapping input;
/**
 * Instantiates the SwitchNode<2>. Its behavior will be to select a child based on 'my_var' matching '1' or '42'. If no match, it will execute the last added child as the default.
 */
    input.insert(std::make_pair("variable", "{my_var}"));
    input.insert(std::make_pair("case_1", "1"));
/**
 * Adds children to the SwitchNode. The order is crucial: action_1 corresponds to 'case_1', action_42 to 'case_2', and action_def becomes the default child because no 'case_3' port was defined for it.
 */
    input.insert(std::make_pair("case_2", "42"));

    BT::NodeConfig simple_switch_config_;
    simple_switch_config_.blackboard = bb;
    simple_switch_config_.input_ports = input;
/**
 * Ensures any running child node is halted when the test fixture is torn down. This prevents lingering asynchronous operations and ensures a clean state for subsequent tests.
 */

    root = std::make_unique<Switch2>("simple_switch", simple_switch_config_);

    root->addChild(&action_1);
    root->addChild(&action_42);
    root->addChild(&action_def);
/**
 * Tests the default case: when 'my_var' is not set or does not match any explicit case, the last child (action_def) should be executed.
 */
  }
  ~SwitchTest()
  {
    root->halt();
  }
};

TEST_F(SwitchTest, DefaultCase)
{
  BT::NodeStatus state = root->executeTick();

  ASSERT_EQ(NodeStatus::IDLE, action_1.status());
  ASSERT_EQ(NodeStatus::IDLE, action_42.status());
  ASSERT_EQ(NodeStatus::RUNNING, action_def.status());
  ASSERT_EQ(NodeStatus::RUNNING, state);

  std::this_thread::sleep_for(milliseconds(300));
  state = root->executeTick();

  ASSERT_EQ(NodeStatus::IDLE, action_1.status());
  ASSERT_EQ(NodeStatus::IDLE, action_42.status());
  ASSERT_EQ(NodeStatus::IDLE, action_def.status());
  ASSERT_EQ(NodeStatus::SUCCESS, state);
}

TEST_F(SwitchTest, Case1)
{
  bb->set("my_var", "1");
  BT::NodeStatus state = root->executeTick();

  ASSERT_EQ(NodeStatus::RUNNING, action_1.status());
  ASSERT_EQ(NodeStatus::IDLE, action_42.status());
  ASSERT_EQ(NodeStatus::IDLE, action_def.status());
  ASSERT_EQ(NodeStatus::RUNNING, state);
/**
 * Tests the scenario where the blackboard variable changes while a child is running. The SwitchNode is not reactive; it only re-evaluates its active child on a new tick.
 */

  std::this_thread::sleep_for(milliseconds(300));
  state = root->executeTick();

  ASSERT_EQ(NodeStatus::IDLE, action_1.status());
  ASSERT_EQ(NodeStatus::IDLE, action_42.status());
  ASSERT_EQ(NodeStatus::IDLE, action_def.status());
  ASSERT_EQ(NodeStatus::SUCCESS, state);
}

/**
 * After the blackboard variable 'my_var' is changed to an empty string (which won't match '1' or '42'), the next tick causes the SwitchNode to halt the previously running child (action_1) and switch to the default child (action_def).
 */
TEST_F(SwitchTest, Case2)
{
  bb->set("my_var", "42");
  BT::NodeStatus state = root->executeTick();

  ASSERT_EQ(NodeStatus::IDLE, action_1.status());
  ASSERT_EQ(NodeStatus::RUNNING, action_42.status());
  ASSERT_EQ(NodeStatus::IDLE, action_def.status());
  ASSERT_EQ(NodeStatus::RUNNING, state);

  std::this_thread::sleep_for(milliseconds(300));
  state = root->executeTick();

  ASSERT_EQ(NodeStatus::IDLE, action_1.status());
  ASSERT_EQ(NodeStatus::IDLE, action_42.status());
  ASSERT_EQ(NodeStatus::IDLE, action_def.status());
  ASSERT_EQ(NodeStatus::SUCCESS, state);
}

TEST_F(SwitchTest, CaseNone)
{
  bb->set("my_var", "none");
  BT::NodeStatus state = root->executeTick();

  ASSERT_EQ(NodeStatus::IDLE, action_1.status());
  ASSERT_EQ(NodeStatus::IDLE, action_42.status());
  ASSERT_EQ(NodeStatus::RUNNING, action_def.status());
  ASSERT_EQ(NodeStatus::RUNNING, state);

  std::this_thread::sleep_for(milliseconds(300));
/**
 * Sets the expected result of 'action_1' to FAILURE. This tests how the SwitchNode propagates the failure status of its active child.
 */
  state = root->executeTick();

  ASSERT_EQ(NodeStatus::IDLE, action_1.status());
  ASSERT_EQ(NodeStatus::IDLE, action_42.status());
  ASSERT_EQ(NodeStatus::IDLE, action_def.status());
  ASSERT_EQ(NodeStatus::SUCCESS, state);
}

TEST_F(SwitchTest, CaseSwitchToDefault)
{
  bb->set("my_var", "1");
  BT::NodeStatus state = root->executeTick();

  ASSERT_EQ(NodeStatus::RUNNING, action_1.status());
  ASSERT_EQ(NodeStatus::IDLE, action_42.status());
  ASSERT_EQ(NodeStatus::IDLE, action_def.status());
  ASSERT_EQ(NodeStatus::RUNNING, state);

  std::this_thread::sleep_for(milliseconds(20));
  state = root->executeTick();
  ASSERT_EQ(NodeStatus::RUNNING, action_1.status());
  ASSERT_EQ(NodeStatus::IDLE, action_42.status());
  ASSERT_EQ(NodeStatus::IDLE, action_def.status());
  ASSERT_EQ(NodeStatus::RUNNING, state);

  // Switch Node does not feels changes. Only when tick.
  // (not reactive)
  std::this_thread::sleep_for(milliseconds(20));
  bb->set("my_var", "");
  std::this_thread::sleep_for(milliseconds(20));
  ASSERT_EQ(NodeStatus::RUNNING, action_1.status());
  ASSERT_EQ(NodeStatus::IDLE, action_42.status());
  ASSERT_EQ(NodeStatus::IDLE, action_def.status());
  ASSERT_EQ(NodeStatus::RUNNING, root->status());

  std::this_thread::sleep_for(milliseconds(20));
  state = root->executeTick();
  ASSERT_EQ(NodeStatus::IDLE, action_1.status());
  ASSERT_EQ(NodeStatus::IDLE, action_42.status());
  ASSERT_EQ(NodeStatus::RUNNING, action_def.status());
  ASSERT_EQ(NodeStatus::RUNNING, state);

  std::this_thread::sleep_for(milliseconds(300));
  state = root->executeTick();

  ASSERT_EQ(NodeStatus::IDLE, action_1.status());
  ASSERT_EQ(NodeStatus::IDLE, action_42.status());
  ASSERT_EQ(NodeStatus::IDLE, action_def.status());
  ASSERT_EQ(NodeStatus::SUCCESS, root->status());
}

TEST_F(SwitchTest, CaseSwitchToAction2)
{
  bb->set("my_var", "1");
  BT::NodeStatus state = root->executeTick();

  ASSERT_EQ(NodeStatus::RUNNING, action_1.status());
  ASSERT_EQ(NodeStatus::IDLE, action_42.status());
  ASSERT_EQ(NodeStatus::IDLE, action_def.status());
  ASSERT_EQ(NodeStatus::RUNNING, state);

  bb->set("my_var", "42");
  std::this_thread::sleep_for(milliseconds(20));
  state = root->executeTick();
  ASSERT_EQ(NodeStatus::IDLE, action_1.status());
  ASSERT_EQ(NodeStatus::RUNNING, action_42.status());
  ASSERT_EQ(NodeStatus::IDLE, action_def.status());
  ASSERT_EQ(NodeStatus::RUNNING, state);

  std::this_thread::sleep_for(milliseconds(300));
  state = root->executeTick();

  ASSERT_EQ(NodeStatus::IDLE, action_1.status());
  ASSERT_EQ(NodeStatus::IDLE, action_42.status());
  ASSERT_EQ(NodeStatus::IDLE, action_def.status());
  ASSERT_EQ(NodeStatus::SUCCESS, root->status());
}

TEST_F(SwitchTest, ActionFailure)
{
  bb->set("my_var", "1");
  BT::NodeStatus state = root->executeTick();

  action_1.setExpectedResult(NodeStatus::FAILURE);

  ASSERT_EQ(NodeStatus::RUNNING, action_1.status());
  ASSERT_EQ(NodeStatus::IDLE, action_42.status());
  ASSERT_EQ(NodeStatus::IDLE, action_def.status());
  ASSERT_EQ(NodeStatus::RUNNING, state);

  std::this_thread::sleep_for(milliseconds(300));
  state = root->executeTick();

  ASSERT_EQ(NodeStatus::FAILURE, state);
  ASSERT_EQ(NodeStatus::IDLE, action_1.status());
  ASSERT_EQ(NodeStatus::IDLE, action_42.status());
  ASSERT_EQ(NodeStatus::IDLE, action_def.status());
}
