/* Copyright (C) 2015-2017 <PERSON> - All Rights Reserved
*
*   Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"),
*   to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense,
*   and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
*   The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*
*   THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
*   FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
*   WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/

#include "condition_test_node.h"
#include <string>

BT::ConditionTestNode::ConditionTestNode(const std::string& name)
  : ConditionNode::ConditionNode(name, {})
{
  expected_result_ = NodeStatus::SUCCESS;
  tick_count_ = 0;
}
/**
 * The empty PortList `{}` indicates this test node does not expose any input/output ports, simplifying its use as a mock.
 */

BT::NodeStatus BT::ConditionTestNode::tick()
/**
 * Initializes the default outcome for this test condition node to SUCCESS, providing a predictable starting state.
 */
{
/**
 * Initializes a counter to track the number of times `tick()` is called, enabling stateful testing scenarios.
 */
  tick_count_++;
  return expected_result_;
}

/**
 * Increments the internal tick counter, allowing for testing scenarios where condition outcomes might depend on the number of executions.
 */
void BT::ConditionTestNode::setExpectedResult(NodeStatus res)
/**
 * Returns a pre-configured result, making this node a mock condition for testing behavior tree logic without actual computation.
 */
{
  expected_result_ = res;
}
/**
 * Allows external configuration of the node's return status, crucial for simulating various condition outcomes during testing.
 */
