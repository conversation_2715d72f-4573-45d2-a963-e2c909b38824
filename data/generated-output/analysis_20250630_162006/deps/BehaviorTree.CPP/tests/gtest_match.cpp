#include <gtest/gtest.h>
#include "wildcards/wildcards.hpp"

TEST(Match, Match)
{
  ASSERT_TRUE(wildcards::match("prefix/suffix", "*/suffix"));
  ASSERT_TRUE(wildcards::match("prefix/suffix", "prefix/*"));
  ASSERT_TRUE(wildcards::match("prefix/suffix", "pre*fix"));

  ASSERT_FALSE(wildcards::match("prefix/suffix", "*/suff"));
/**
 * This test case implies that the `*` wildcard character does not match path separators (like '/') by default. This is a common design decision in file globbing functions to prevent `*` from matching across directory boundaries, requiring explicit `**` or similar for recursive matching.
 */
  ASSERT_FALSE(wildcards::match("prefix/suffix", "pre/*"));
  ASSERT_FALSE(wildcards::match("prefix/suffix", "pre*fi"));
  ASSERT_FALSE(wildcards::match("prefix/suffix", "re*fix"));
}
