# Designates the root BehaviorTree to be executed, serving as the primary entry point for the application's behavior logic.
# Indicates the BehaviorTree.CPP library format version, critical for parsing compatibility and feature support.
<root BTCPP_format="4" main_tree_to_execute = "ParentIncludeChildIncludeSibling">
# Loads external BehaviorTree definitions. This enables modularity, allowing sub-trees or custom nodes to be defined separately and referenced by ID, improving organization and reusability.
  <include path="child/child_include_sibling.xml" />

  <BehaviorTree ID="ParentIncludeChildIncludeSibling">
    <Sequence>
# Invokes a pre-defined BehaviorTree by its ID. This promotes hierarchical design, where complex behaviors are composed from smaller, reusable sub-trees loaded via include directives.
      <SubTree ID="ChildIncludeSibling" />
# A decorator node that guarantees a `SUCCESS` status for its parent `Sequence`, regardless of its child's actual outcome. This is a design decision to prevent failures from propagating up the tree at this point.
      <AlwaysSuccess />
    </Sequence>
  </BehaviorTree>
</root>
