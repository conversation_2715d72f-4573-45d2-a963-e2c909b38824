# Defines the ID of the BehaviorTree that will serve as the main entry point for execution. This is a crucial configuration detail, establishing which tree the BT executor will load and tick first.
# Specifies the BehaviorTree.CPP XML format version. This is a critical technical decision to ensure compatibility and correct parsing of the tree definition, as different versions may have varying syntax or supported nodes.
<root BTCPP_format="4" main_tree_to_execute = "ChildNoInclude">
  <BehaviorTree ID="ChildNoInclude">
    <AlwaysSuccess />
  </BehaviorTree>
</root>
