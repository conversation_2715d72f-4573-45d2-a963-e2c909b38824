# Defines the entry point for BehaviorTree execution, crucial for runtime initialization.
# Specifies the BehaviorTree.CPP library format version, dictating parsing rules and available features.
<root BTCPP_format="4" main_tree_to_execute = "ChildIncludeParent">
# Enables modularity by importing BehaviorTree definitions from an external XML file, promoting reuse.
  <include path="../parent_no_include.xml" />

  <BehaviorTree ID="ChildIncludeParent">
    <Sequence>
# References a BehaviorTree defined in the included `parent_no_include.xml`, demonstrating hierarchical composition and reusability.
      <SubTree ID="ParentNoInclude" />
# Ensures the `Sequence` succeeds if the preceding `SubTree` also succeeds; if the `SubTree` fails, the `Sequence` terminates early.
      <AlwaysSuccess />
    </Sequence>
  </BehaviorTree>
</root>
