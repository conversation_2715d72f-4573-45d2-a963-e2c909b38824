# Defines the entry point for tree execution, indicating which top-level BehaviorTree ID will be instantiated and run by the engine.
# Specifies the BehaviorTree.CPP XML format version, crucial for parser compatibility and ensuring correct interpretation of the tree structure.
<root BTCPP_format="4" main_tree_to_execute = "ParentIncludeChild">
# Enables modularity and reusability by loading external BehaviorTree definitions from 'child/child_no_include.xml' into the current scope, making them available for composition.
  <include path="child/child_no_include.xml" />

  <BehaviorTree ID="ParentIncludeChild">
    <Sequence>
# This node dynamically embeds and executes another BehaviorTree identified by 'ChildNoInclude'. Its availability relies on the 'include' directive on line 2, demonstrating a core mechanism for tree composition and hierarchy.
      <SubTree ID="ChildNoInclude" />
# Ensures the parent Sequence always succeeds after the 'ChildNoInclude' subtree completes, preventing the sequence from failing even if the subtree itself returns a failure status. This is a common pattern for robust flow control.
      <AlwaysSuccess />
    </Sequence>
  </BehaviorTree>
</root>
