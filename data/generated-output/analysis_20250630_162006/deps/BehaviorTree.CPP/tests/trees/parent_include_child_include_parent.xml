# Defines the explicit entry point for behavior tree execution, ensuring a specific top-level tree is run.
# Specifies the BehaviorTree.CPP XML format version, dictating parsing rules and available features.
<root BTCPP_format="4" main_tree_to_execute = "ParentIncludeChildIncludeParent">
# Enables modularity and reusability by loading external behavior tree definitions, promoting organized design.
  <include path="child/child_include_parent.xml" />

  <BehaviorTree ID="ParentIncludeChildIncludeParent">
    <Sequence>
# Instantiates the included 'ChildIncludeParent' tree as a reusable component, demonstrating hierarchical composition.
      <SubTree ID="ChildIncludeParent" />
# Ensures the parent 'Sequence' always returns 'SUCCESS', regardless of the 'SubTree' outcome, to guarantee subsequent node execution or overall tree success.
      <AlwaysSuccess />
    </Sequence>
  </BehaviorTree>
</root>
