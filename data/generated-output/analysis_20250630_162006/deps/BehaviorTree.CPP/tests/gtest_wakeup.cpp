#include <gtest/gtest.h>
#include "behaviortree_cpp/bt_factory.h"

using namespace BT;

class FastAction : public BT::ThreadedAction
/**
 * Inherits `ThreadedAction` to enable asynchronous execution of `tick()` in a separate thread, preventing the main behavior tree loop from blocking.
 */
{
public:
  // Any TreeNode with ports must have a constructor with this signature
  FastAction(const std::string& name, const BT::NodeConfig& config)
    : ThreadedAction(name, config)
/**
 * Constructor signature required by `BehaviorTreeFactory` for node instantiation, ensuring proper initialization from XML definitions.
 */
  {}

  static BT::PortsList providedPorts()
  {
    return {};
  }

  BT::NodeStatus tick() override
/**
 * Simulates a time-consuming, non-blocking operation, demonstrating the asynchronous nature of `ThreadedAction`.
 */
  {
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    return BT::NodeStatus::SUCCESS;
  }
};

TEST(WakeUp, BasicTest)
{
  static const char* xml_text = R"(

<root BTCPP_format="4">
    <BehaviorTree ID="MainTree">
        <FastAction/>
/**
 * Registers the custom C++ node with the factory, allowing it to be instantiated from the XML definition `<FastAction/>`.
 */
    </BehaviorTree>
</root> )";

  BehaviorTreeFactory factory;
  factory.registerNodeType<FastAction>("FastAction");
/**
 * Initiates the behavior tree execution. For `ThreadedAction` nodes, this starts their `tick()` method in a separate thread and returns immediately, without waiting for completion.
 */

/**
 * Allows sufficient time for any asynchronous `ThreadedAction` nodes (like `FastAction`) to complete their operations, without blocking the main thread for the full duration if the action finishes earlier.
 */
  Tree tree = factory.createTreeFromText(xml_text);

  using namespace std::chrono;

  auto t1 = system_clock::now();
  tree.tickOnce();
/**
 * Asserts that the total elapsed time is minimal, confirming that `tree.tickOnce()` did not block for the `FastAction`'s simulated 10ms delay, and `tree.sleep()` efficiently managed the asynchronous execution.
 */
  tree.sleep(milliseconds(200));
  auto t2 = system_clock::now();

  auto dT = duration_cast<milliseconds>(t2 - t1).count();
  std::cout << "Woke up after msec: " << dT << std::endl;

  ASSERT_LT(dT, 25);
}
