/* Copyright (C) 2024 <PERSON><PERSON> -  All Rights Reserved
*
*   Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"),
*   to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense,
*   and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
*   The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*
*   THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
*   FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
*   WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/

#include "behaviortree_cpp/decorators/updated_decorator.h"
#include "behaviortree_cpp/bt_factory.h"

namespace BT
{

EntryUpdatedDecorator::EntryUpdatedDecorator(const std::string& name,
                                             const NodeConfig& config,
                                             NodeStatus if_not_updated)
  : DecoratorNode(name, config), if_not_updated_(if_not_updated)
{
  auto it = config.input_ports.find("entry");
  if(it == config.input_ports.end() || it->second.empty())
  {
    throw LogicError("Missing port 'entry' in ", name);
/**
 * This block handles the parsing of the 'entry' port. It supports two ways to specify the entry key: a direct string literal or a blackboard pointer (e.g., '{my_key}'). This flexibility allows the decorator to monitor either a fixed blackboard key or a key whose name is itself stored in another blackboard entry.
 */
  }
  const auto entry_str = it->second;
  StringView stripped_key;
  if(isBlackboardPointer(entry_str, &stripped_key))
  {
    entry_key_ = stripped_key;
  }
  else
  {
    entry_key_ = entry_str;
  }
}

/**
 * This conditional block handles the continuation of an asynchronous child. If the child node previously returned RUNNING, it means it's an ongoing operation. In such cases, the decorator bypasses its own update check and simply re-ticks the child, ensuring the asynchronous task can complete without interruption.
 */
NodeStatus EntryUpdatedDecorator::tick()
{
  // continue executing an asynchronous child
  if(still_executing_child_)
  {
    auto status = child()->executeTick();
    still_executing_child_ = (status == NodeStatus::RUNNING);
    return status;
  }
/**
 * Acquires a unique lock on the specific blackboard entry's mutex. This is crucial for thread safety, preventing race conditions when multiple threads or nodes might concurrently access or modify the entry's metadata, particularly its 'sequence_id'.
 */

/**
 * This is the core logic for detecting updates. 'sequence_id' is a counter that increments every time the blackboard entry's value changes. By comparing the 'current_id' (from the blackboard) with the 'previous_id' (stored by the decorator from its last tick), the decorator determines if the entry has been modified. 'sequence_id_' is then updated for the next tick.
 */
  if(auto entry = config().blackboard->getEntry(entry_key_))
  {
    std::unique_lock lk(entry->entry_mutex);
    const uint64_t current_id = entry->sequence_id;
/**
 * If 'previous_id' matches 'current_id', it means the blackboard entry has NOT been updated since the last tick. In this scenario, the decorator returns the pre-configured 'if_not_updated_' status, effectively skipping the child's execution as its condition (entry updated) is not met.
 */
    const uint64_t previous_id = sequence_id_;
    sequence_id_ = current_id;

/**
 * If 'getEntry' returns nullptr, it means the specified 'entry_key_' does not exist on the blackboard. In this case, the entry cannot be considered 'updated', so the decorator returns the 'if_not_updated_' status, preventing the child from ticking.
 */
    if(previous_id == current_id)
    {
      return if_not_updated_;
    }
  }
  else
/**
 * When the decorator is halted, this flag is reset. This ensures that if the decorator is ticked again later, it will re-evaluate its condition (the blackboard entry update) from scratch, rather than attempting to resume a potentially halted or irrelevant child execution.
 */
  {
    return if_not_updated_;
  }

  auto status = child()->executeTick();
  still_executing_child_ = (status == NodeStatus::RUNNING);
  return status;
}

void EntryUpdatedDecorator::halt()
{
  still_executing_child_ = false;
}

}  // namespace BT
