#include "behaviortree_cpp/decorators/subtree_node.h"

BT::SubTreeNode::SubTreeNode(const std::string& name, const NodeConfig& config)
  : DecoratorNode(name, config)
{
/**
 * Sets the unique identifier for this node type, enabling its instantiation via the BehaviorTreeFactory using the string 'SubTree'.
 */
  setRegistrationID("SubTree");
}

BT::PortsList BT::SubTreeNode::providedPorts()
{
  auto port =
      PortInfo(PortDirection::INPUT, typeid(bool), GetAnyFromStringFunctor<bool>());
  port.setDefaultValue(false);
  port.setDescription("If true, all the ports with the same name "
                      "will be remapped");
/**
 * Declares a special port `_autoremap`. If true, the SubTreeNode automatically remaps all ports of the same name between the parent and the subtree, simplifying port configuration.
 */

  return { { "_autoremap", port } };
}

BT::NodeStatus BT::SubTreeNode::tick()
{
/**
 * Transitions the node's status from IDLE to RUNNING on the first tick. This is a standard BehaviorTree pattern to indicate that the node has started active execution.
 */
  NodeStatus prev_status = status();
  if(prev_status == NodeStatus::IDLE)
  {
    setStatus(NodeStatus::RUNNING);
  }
/**
 * Resets the child node's state if it has completed its execution (SUCCESS or FAILURE). This ensures the child will restart from IDLE on subsequent ticks, allowing for re-execution of the subtree.
 */
  const NodeStatus child_status = child_node_->executeTick();
  if(isStatusCompleted(child_status))
  {
    resetChild();
  }

  return child_status;
}
