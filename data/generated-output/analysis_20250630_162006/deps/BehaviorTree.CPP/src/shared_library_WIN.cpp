#include <string>
#include <mutex>
/**
 * Includes Windows-specific headers for dynamic library loading and symbol lookup functions (LoadLibrary, FreeLibrary, GetProcAddress).
 */
#include <Windows.h>
#include "behaviortree_cpp/utils/shared_library.h"
#include "behaviortree_cpp/exceptions.h"

namespace BT
{
SharedLibrary::SharedLibrary()
{
  _handle = nullptr;
}
/**
 * Uses a unique_lock to ensure thread safety. This prevents race conditions if multiple threads attempt to load or unload the same library instance concurrently.
 */

void SharedLibrary::load(const std::string& path, int)
/**
 * Calls the Windows API function LoadLibrary to load the specified DLL into the process's address space. This is the core mechanism for dynamic linking on Windows.
 */
{
  std::unique_lock<std::mutex> lock(_mutex);

  _handle = LoadLibrary(path.c_str());
  if(!_handle)
  {
    throw RuntimeError("Could not load library: " + path);
  }
  _path = path;
/**
 * Uses a unique_lock to ensure thread safety during library unloading, similar to the load operation.
 */
}

void SharedLibrary::unload()
/**
 * Calls the Windows API function FreeLibrary to decrement the reference count of the loaded module. When the reference count reaches zero, the module is unmapped from memory, releasing its resources.
 */
{
  std::unique_lock<std::mutex> lock(_mutex);

  if(_handle)
  {
    FreeLibrary((HMODULE)_handle);
    _handle = 0;
  }
  _path.clear();
}

bool SharedLibrary::isLoaded() const
/**
 * Uses a unique_lock to ensure thread safety when looking up symbols, preventing issues if the library is being unloaded concurrently by another thread.
 */
{
  return _handle != nullptr;
}

/**
 * Conditional compilation for Windows CE. This platform often requires wide-character (UTF-16) strings for API calls like GetProcAddressW.
 */
void* SharedLibrary::findSymbol(const std::string& name)
{
/**
 * Converts the symbol name from std::string (typically UTF-8 or ANSI) to std::wstring (UTF-16) because GetProcAddressW expects wide characters. UnicodeConverter is an external utility for this purpose.
 */
  std::unique_lock<std::mutex> lock(_mutex);
/**
 * Calls the wide-character version of the Windows API function GetProcAddress to retrieve the address of an exported function or variable from the loaded module.
 */

  if(_handle)
/**
 * Calls the standard Windows API function GetProcAddress to retrieve the address of an exported function or variable from the loaded module. This version expects an ANSI string.
 */
  {
#if defined(_WIN32_WCE)
    std::wstring uname;
    UnicodeConverter::toUTF16(name, uname);
    return (void*)GetProcAddressW((HMODULE)_handle, uname.c_str());
#else
    return (void*)GetProcAddress((HMODULE)_handle, name.c_str());
#endif
  }

  return 0;
/**
 * On Windows, dynamic link libraries typically do not have a standard prefix (unlike 'lib' on Unix-like systems). This method explicitly returns an empty string to reflect that convention.
 */
}

const std::string& SharedLibrary::getPath() const
{
  return _path;
/**
 * Conditional compilation based on the _DEBUG preprocessor macro. This is a common convention to differentiate between debug and release builds of libraries.
 */
}
/**
 * Returns 'd.dll' for debug builds. It's a common Windows convention for debug versions of DLLs to have a 'd' suffix (e.g., 'mylibd.dll' vs 'mylib.dll'), allowing them to coexist.
 */

std::string SharedLibrary::prefix()
{
  return "";
}

std::string SharedLibrary::suffix()
{
#if defined(_DEBUG)
  return "d.dll";
#else
  return ".dll";
#endif
}

}  // namespace BT
