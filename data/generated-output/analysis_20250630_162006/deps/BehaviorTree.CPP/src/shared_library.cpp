#include "behaviortree_cpp/utils/shared_library.h"
#include "behaviortree_cpp/exceptions.h"

BT::SharedLibrary::SharedLibrary(const std::string& path, int flags)
{
/**
 * Delegates actual library loading to a separate method. This design allows for potential deferred loading, re-loading, or more complex error handling outside the constructor.
 */
  load(path, flags);
}

void* BT::SharedLibrary::getSymbol(const std::string& name)
{
  void* result = findSymbol(name);
  if(result)
    return result;
  else
/**
 * Throws an exception if the symbol is not found. This enforces a strict contract for `getSymbol`, preventing silent failures and forcing callers to explicitly handle the absence of a required symbol.
 */
    throw RuntimeError("[SharedLibrary::getSymbol]: can't find symbol ", name);
}

bool BT::SharedLibrary::hasSymbol(const std::string& name)
{
/**
 * Provides a non-throwing way to check for a symbol's existence. This allows callers to gracefully handle optional symbols without relying on exception catching for control flow, complementing the strict `getSymbol`.
 */
  return findSymbol(name) != nullptr;
}

std::string BT::SharedLibrary::getOSName(const std::string& name)
{
/**
 * Abstracts away OS-specific naming conventions (e.g., 'lib' prefix, '.so'/'dll' suffix) for shared libraries. This is crucial for cross-platform compatibility, as the logical library name needs to be translated to the actual file name on disk.
 */
  return prefix() + name + suffix();
}
