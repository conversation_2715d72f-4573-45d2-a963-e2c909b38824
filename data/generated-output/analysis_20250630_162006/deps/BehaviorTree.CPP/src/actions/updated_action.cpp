/* Copyright (C) 2024 <PERSON><PERSON> -  All Rights Reserved
*
*   Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"),
*   to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense,
*   and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
*   The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*
*   THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
*   FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
*   WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/

#include "behaviortree_cpp/actions/updated_action.h"
#include "behaviortree_cpp/bt_factory.h"

namespace BT
{

EntryUpdatedAction::EntryUpdatedAction(const std::string& name, const NodeConfig& config)
/**
 * Validates that the 'entry' input port exists and is not empty. An empty string for a port typically indicates an invalid or unconnected state.
 */
  : SyncActionNode(name, config)
{
  auto it = config.input_ports.find("entry");
  if(it == config.input_ports.end() || it->second.empty())
  {
/**
 * Determines if the 'entry' port value is a literal Blackboard key or a Blackboard pointer (e.g., '{my_key}'). This allows the action to dynamically resolve the target entry based on the pointer's value.
 */
    throw LogicError("Missing port 'entry' in ", name);
  }
  const auto entry_str = it->second;
  StringView stripped_key;
  if(isBlackboardPointer(entry_str, &stripped_key))
  {
    entry_key_ = stripped_key;
  }
  else
/**
 * Acquires a unique lock on the specific Blackboard entry's mutex. This is crucial for thread-safety, preventing race conditions when accessing or modifying the entry's metadata (like sequence_id) from multiple threads or nodes concurrently.
 */
  {
/**
 * Retrieves the current sequence ID of the Blackboard entry. This ID acts as a version counter, incrementing every time the entry's value is modified, allowing detection of changes.
 */
    entry_key_ = entry_str;
  }
/**
 * Updates the internal `sequence_id_` member of *this specific action instance* to the `current_id`. This stores the last observed version of the entry, enabling the node to detect if the entry has changed on its *next* tick.
 */
}
/**
 * This commented-out block represents an alternative, older approach that would have used a global registry to track sequence IDs. The current implementation (using a member variable `sequence_id_`) is preferred because it makes each `EntryUpdatedAction` instance self-contained, tracking its *own* observation history for an entry, rather than relying on shared global state. This simplifies concurrency and makes the node's behavior more predictable for its specific purpose.
 */

NodeStatus EntryUpdatedAction::tick()
{
  if(auto entry = config().blackboard->getEntry(entry_key_))
  {
    std::unique_lock lk(entry->entry_mutex);
    const uint64_t current_id = entry->sequence_id;
    const uint64_t previous_id = sequence_id_;
    sequence_id_ = current_id;
    /*
    uint64_t previous_id = 0;
    auto& previous_id_registry = details::GlobalSequenceRegistry();
/**
 * Returns SUCCESS if the `current_id` (the entry's current version) is different from `previous_id` (the version seen by this node on its last tick), indicating the entry has been updated. Otherwise, returns FAILURE.
 */

    // find the previous id in the registry.
    auto it = previous_id_registry.find(entry.get());
    if(it != previous_id_registry.end())
    {
      previous_id = it->second;
    }
    if(previous_id != current_id)
    {
      previous_id_registry[entry.get()] = current_id;
    }*/
    return (previous_id != current_id) ? NodeStatus::SUCCESS : NodeStatus::FAILURE;
  }
  else
  {
    return NodeStatus::FAILURE;
  }
}

}  // namespace BT
