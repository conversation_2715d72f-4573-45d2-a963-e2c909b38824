/* Copyright (C) 2020 <PERSON><PERSON>, Eurecat -  All Rights Reserved
*
*   Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"),
*   to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense,
*   and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
*   The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*
*   THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
*   FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
*   WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/

#include "behaviortree_cpp/controls/reactive_fallback.h"

namespace BT
/**
 * Static flag to enable/disable an exception when multiple children return RUNNING. This enforces a strict 'only one active child' policy, which is a key characteristic of ReactiveFallback.
 */
{

bool ReactiveFallback::throw_if_multiple_running = false;

void ReactiveFallback::EnableException(bool enable)
{
  ReactiveFallback::throw_if_multiple_running = enable;
}

NodeStatus ReactiveFallback::tick()
/**
 * Resets the internal state tracking the running child only when the ReactiveFallback itself is starting from an IDLE state. This ensures continuity if the node was previously RUNNING.
 */
{
  bool all_skipped = true;
  if(status() == NodeStatus::IDLE)
  {
    running_child_ = -1;
  }
  setStatus(NodeStatus::RUNNING);

  for(size_t index = 0; index < childrenCount(); index++)
  {
    TreeNode* current_child_node = children_nodes_[index];
/**
 * Tracks if all children processed so far have returned SKIPPED. This is used to determine the final status if no child succeeds or runs.
 */
    const NodeStatus child_status = current_child_node->executeTick();

    // switch to RUNNING state as soon as you find an active child
    all_skipped &= (child_status == NodeStatus::SKIPPED);

/**
 * This is the core 'reactive' behavior: if a child starts running, all other children (including any that might have been running previously) are immediately halted. This ensures only one child is active at any given time, preventing unintended parallel execution.
 */
    switch(child_status)
    {
      case NodeStatus::RUNNING: {
        // reset the previous children, to make sure that they are
        // in IDLE state the next time we tick them
        for(size_t i = 0; i < childrenCount(); i++)
        {
          if(i != index)
          {
/**
 * Throws an error if a different child returns RUNNING while another child was already marked as running. This enforces the design principle that only one child should be actively executing in a ReactiveFallback, preventing ambiguous states.
 */
            haltChild(i);
          }
        }
        if(running_child_ == -1)
        {
          running_child_ = int(index);
        }
        else if(throw_if_multiple_running && running_child_ != int(index))
        {
          throw LogicError("[ReactiveFallback]: only a single child can return RUNNING.\n"
                           "This throw can be disabled with "
                           "ReactiveFallback::EnableException(false)");
        }
/**
 * Resets all children to IDLE. This is crucial because a successful execution means the entire fallback sequence is complete, and children should be ready for a fresh start on the next tick.
 */
        return NodeStatus::RUNNING;
      }

      case NodeStatus::FAILURE:
        break;

/**
 * A SKIPPED child must be halted (reset to IDLE) to allow it to be skipped again or properly ticked in a subsequent cycle. SKIPPED implies the node chose not to execute, not that it completed or failed.
 */
      case NodeStatus::SUCCESS: {
        resetChildren();
        return NodeStatus::SUCCESS;
      }
/**
 * A child node should never return IDLE after being ticked. This indicates a fundamental logic error in the child's implementation, as `executeTick()` is expected to produce a definitive status (RUNNING, SUCCESS, FAILURE, SKIPPED).
 */

      case NodeStatus::SKIPPED: {
        // to allow it to be skipped again, we must reset the node
        haltChild(index);
      }
/**
 * If the loop completes without a child returning SUCCESS or RUNNING, it means all children either failed or were skipped. In this scenario, all children must be reset to IDLE for the next tick cycle.
 */
      break;

      case NodeStatus::IDLE: {
/**
 * Determines the final status: if all children were SKIPPED, the ReactiveFallback itself returns SKIPPED (indicating nothing was done). Otherwise (if at least one child failed and none succeeded or ran), it returns FAILURE.
 */
        throw LogicError("[", name(), "]: A children should not return IDLE");
      }
    }  // end switch
  }    //end for

/**
 * Clears the internal state tracking the running child. This ensures that when the ReactiveFallback is halted, it's prepared for a clean start on its next tick, without retaining a reference to a potentially stale running child.
 */
  resetChildren();

  // Skip if ALL the nodes have been skipped
  return all_skipped ? NodeStatus::SKIPPED : NodeStatus::FAILURE;
}

void ReactiveFallback::halt()
{
  running_child_ = -1;
  ControlNode::halt();
}

}  // namespace BT
