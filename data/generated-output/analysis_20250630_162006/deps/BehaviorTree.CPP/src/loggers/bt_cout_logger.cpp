#include "behaviortree_cpp/loggers/bt_cout_logger.h"

namespace BT
{

StdCoutLogger::StdCoutLogger(const BT::Tree& tree) : StatusChangeLogger(tree.rootNode())
{}
/**
 * Initializes the base logger to monitor status changes for all nodes within the Behavior Tree, starting from its root. This ensures comprehensive logging of the entire tree's execution.
 */
StdCoutLogger::~StdCoutLogger()
{}

void StdCoutLogger::callback(Duration timestamp, const TreeNode& node,
                             NodeStatus prev_status, NodeStatus status)
{
  using namespace std::chrono;

  constexpr const char* whitespaces = "                         ";
  constexpr const size_t ws_count = 25;

/**
 * Calculates the starting point in the 'whitespaces' string to achieve a fixed-width column for the node name. This ensures consistent alignment of subsequent status information, improving readability in console output.
 */
  double since_epoch = duration<double>(timestamp).count();
  printf("[%.3f]: %s%s %s -> %s", since_epoch, node.name().c_str(),
         &whitespaces[std::min(ws_count, node.name().size())],
         toStr(prev_status, true).c_str(), toStr(status, true).c_str());
  std::cout << std::endl;
/**
 * Explicitly flushes the output buffer. This is crucial for real-time logging to ensure that messages are displayed immediately, rather than being buffered and potentially delayed.
 */
}

void StdCoutLogger::flush()
{
  std::cout << std::flush;
}

}  // namespace BT
