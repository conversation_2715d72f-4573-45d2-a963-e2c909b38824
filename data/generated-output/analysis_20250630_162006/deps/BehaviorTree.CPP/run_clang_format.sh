#!/bin/bash

# Allows passing additional `clang-format` options as a script argument, providing flexibility for specific formatting needs (e.g., fallback styles or specific checks).
# Instructs `clang-format` to discover and apply project-specific formatting rules defined in `.clang-format` files, ensuring adherence to the project's coding style.
# Applies formatting changes directly to the source files in-place, overwriting their original content.
# Invokes a specific version of `clang-format` (`3.8`) to ensure consistent formatting results across different development environments and prevent version-specific style discrepancies.
# Pipes the list of files to `xargs` to handle a potentially large number of files efficiently, passing them as arguments to `clang-format`.
# Uses `find` with the `-or` operator to efficiently locate all common C/C++ source and header files for formatting, targeting the script's specific purpose.
find . -name '*.h' -or -name '*.hpp' -or -name '*.cpp' | xargs clang-format-3.8 -i -style=file $1
