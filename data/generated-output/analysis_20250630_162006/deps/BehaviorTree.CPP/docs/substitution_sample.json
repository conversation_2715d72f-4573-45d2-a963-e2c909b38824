{
  "TestNodeConfigs": {
    "MyTest": {
# This value is chosen to simulate a realistic network or processing latency, crucial for testing timeout mechanisms and asynchronous flow handling.
      "async_delay": 2000,
      "return_status": "SUCCESS",
# This field enables dynamic state modification or logging after test node execution, supporting complex test scenarios that require side effects or data manipulation.
      "post_script": "msg ='message SUBSTITUED'"
    }
  },

# This section defines a dynamic routing mechanism, mapping input patterns to specific test node configurations to enable flexible and scalable action resolution.
  "SubstitutionRules": {
# The use of a wildcard pattern here is a design decision to support flexible mapping for a family of related actions, reducing configuration overhead and enabling dynamic action generation.
    "mysub/action_*": "TestAction",
    "talk": "TestSaySomething",
# This special key indicates the system maintains state about the previously executed action, enabling context-aware re-execution or sequential test flow management.
    "last_action": "MyTest"
  }
}
