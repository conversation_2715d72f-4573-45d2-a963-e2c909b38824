camera_frame: bottom_back
docking_distance: 0.8 #0.8 for docking stattion
undocking_distance: 1.2
#error_threshold: 0.03 # 0.03
#error_threshold_yaw: 0.04 # 0.05
max_linear_velocity_limit: 0.15 #0.2
min_linear_velocity_limit: 0.06
max_angular_velocity_limit: 0.15 #0.2
min_angular_velocity_limit: 0.05
kp_x: 0.8
kp_y: 0.5
kp_yaw: 0.25
use_sinh_law: true
use_tanh_law: false
enable_backwards_docking: true
marker_y_offset: 0.0

error_threshold_xy: 0.02
error_threshold_yaw: 0.03
error_threshold_virtualdocking_xy: 0.1
error_threshold_virtualdocking_yaw: 0.1
max_action_time_sec: 30.0
max_target_lost_time_for_moving_robot: 0.8
target_lost_time_for_recovery: 30.0
max_docking_retries: 5

communicate_with_dock: true
min_contact_force_threshold: 50
contact_force_threshold: 400
contact_force_buffer_size: 5
use_motor_current_instead_of_contact_force: false
motor_amps_threshold: 40.0
