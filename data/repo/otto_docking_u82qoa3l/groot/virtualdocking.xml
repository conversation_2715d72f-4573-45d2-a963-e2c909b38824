<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
  <BehaviorTree ID="VirtualdockingSubtree">
    <Sequence>
      <Fallback _description="is location reached fallback">
        <Inverter>
          <CheckIfMapPoseIsTooFar target_pose="{target_pose_map_frame}"/>
        </Inverter>
        <ForceFailure>
          <AbortActionGoal/>
        </ForceFailure>
      </Fallback>
      <ReactiveSequence>
        <Fallback>
          <Inverter>
            <IsPreemptRequested/>
          </Inverter>
          <ForceFailure>
            <PremptActionGoal/>
          </ForceFailure>
        </Fallback>
        <Fallback>
          <MoveToMapPose target_pose="{target_pose_map_frame}"/>
          <Inverter>
            <AbortActionGoal/>
          </Inverter>
        </Fallback>
      </ReactiveSequence>
    </Sequence>
  </BehaviorTree>

  <!-- Description of Node Models (used by Groot) -->
  <TreeNodesModel>
    <Condition ID="AbortActionGoal"/>
    <Action ID="CheckIfMapPoseIsTooFar">
      <input_port name="target_pose"
                  type="geometry_msgs::PoseStamped_&lt;std::allocator&lt;void&gt; &gt;"/>
    </Action>
    <Condition ID="IsPreemptRequested"/>
    <Action ID="MoveToMapPose">
      <input_port name="target_pose"
                  type="geometry_msgs::PoseStamped_&lt;std::allocator&lt;void&gt; &gt;"/>
    </Action>
    <Condition ID="PremptActionGoal"/>
  </TreeNodesModel>

</root>
