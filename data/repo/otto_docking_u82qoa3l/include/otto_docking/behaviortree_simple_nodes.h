#pragma once

#include "behaviortree_cpp/bt_factory.h"

#include "ros/ros.h"
#include "std_srvs/SetBool.h"

class ForceApplyBrakes
{
public:
    ForceApplyBrakes(ros::NodeHandle &nh)
        : nh_(nh)
    {
        client_ = nh_.serviceClient<std_srvs::SetBool>(service_name_);
    }

    BT::NodeStatus apply()
    {
        std_srvs::SetBool srv;
        srv.request.data = true;

        if (!client_.call(srv))
        {
            client_ = nh_.serviceClient<std_srvs::SetBool>(service_name_);
            ROS_WARN_STREAM(service_name_ + " call failed");
            return BT::NodeStatus::FAILURE;
        }
        if (!srv.response.success)
        {
            ROS_WARN_STREAM(service_name_ + " call failed, message: " + srv.response.message.c_str());
            return BT::NodeStatus::FAILURE;
        }

        ROS_INFO_STREAM(service_name_ + " call success");
        return BT::NodeStatus::SUCCESS;
    }

    BT::NodeStatus release()
    {
        std_srvs::SetBool srv;
        srv.request.data = false;

        if (!client_.call(srv))
        {
            client_ = nh_.serviceClient<std_srvs::SetBool>(service_name_);
            ROS_WARN_STREAM(service_name_ + " call failed");
            return BT::NodeStatus::FAILURE;
        }
        if (!srv.response.success)
        {
            ROS_WARN_STREAM(service_name_ + " call failed, message: " + srv.response.message.c_str());
            return BT::NodeStatus::FAILURE;
        }

        ROS_INFO_STREAM(service_name_ + " call success");
        return BT::NodeStatus::SUCCESS;
    }

private:
    ros::NodeHandle &nh_;
    std::string service_name_ = "/force_apply_brakes";
    ros::ServiceClient client_;
};

class DockingDriveMode
{
public:
    DockingDriveMode(ros::NodeHandle &nh)
        : nh_(nh)
    {
        client_ = nh_.serviceClient<std_srvs::SetBool>(service_name_);
    }

    BT::NodeStatus start()
    {
        std_srvs::SetBool srv;
        srv.request.data = true;

        if (!client_.call(srv))
        {
            client_ = nh_.serviceClient<std_srvs::SetBool>(service_name_);
            ROS_WARN_STREAM(service_name_ + " call failed");
            return BT::NodeStatus::FAILURE;
        }
        if (!srv.response.success)
        {
            ROS_WARN_STREAM(service_name_ + " call failed, message: " + srv.response.message.c_str());
            return BT::NodeStatus::FAILURE;
        }

        ROS_INFO_STREAM(service_name_ + " call success");
        return BT::NodeStatus::SUCCESS;
    }

    BT::NodeStatus stop()
    {
        std_srvs::SetBool srv;
        srv.request.data = false;

        if (!client_.call(srv))
        {
            client_ = nh_.serviceClient<std_srvs::SetBool>(service_name_);
            ROS_WARN_STREAM(service_name_ + " call failed");
            return BT::NodeStatus::FAILURE;
        }

        if (!srv.response.success)
        {
            ROS_WARN_STREAM(service_name_ + " call failed, message: " + srv.response.message.c_str());
            return BT::NodeStatus::FAILURE;
        }

        ROS_INFO_STREAM(service_name_ + " call success");
        return BT::NodeStatus::SUCCESS;
    }

private:
    ros::NodeHandle &nh_;
    std::string service_name_ = "/ottonomy_robot_node/docking_drive_mode";
    ros::ServiceClient client_;
};

class ApriltagDetection
{
public:
    ApriltagDetection(ros::NodeHandle &nh)
        : nh_(nh)
    {
        gscam_client_ = nh_.serviceClient<std_srvs::SetBool>(gscam_service_name_);
        apriltag_client_ = nh_.serviceClient<std_srvs::SetBool>(apriltag_service_name_);
    }

    BT::NodeStatus start()
    {
        std_srvs::SetBool srv;
        srv.request.data = true;

        if (!gscam_client_.call(srv))
        {
            gscam_client_ = nh_.serviceClient<std_srvs::SetBool>(gscam_service_name_);
            ROS_WARN_STREAM(gscam_service_name_ + " call failed");
            return BT::NodeStatus::FAILURE;
        }
        if (!srv.response.success)
        {
            ROS_WARN_STREAM(gscam_service_name_ + " call failed, message: " + srv.response.message.c_str());
            return BT::NodeStatus::FAILURE;
        }
        ROS_INFO_STREAM(gscam_service_name_ + " call success");

        if (!apriltag_client_.call(srv))
        {
            apriltag_client_ = nh_.serviceClient<std_srvs::SetBool>(apriltag_service_name_);
            ROS_WARN_STREAM(apriltag_service_name_ + " call failed");
            return BT::NodeStatus::FAILURE;
        }
        if (!srv.response.success)
        {
            ROS_WARN_STREAM(apriltag_service_name_ + " call failed, message: " + srv.response.message.c_str());
            return BT::NodeStatus::FAILURE;
        }
        ROS_INFO_STREAM(apriltag_service_name_ + " call success");

        return BT::NodeStatus::SUCCESS;
    }

    BT::NodeStatus stop()
    {
        std_srvs::SetBool srv;
        srv.request.data = false;

        if (!apriltag_client_.call(srv))
        {
            apriltag_client_ = nh_.serviceClient<std_srvs::SetBool>(apriltag_service_name_);
            ROS_WARN_STREAM(apriltag_service_name_ + " call failed");
            return BT::NodeStatus::FAILURE;
        }
        if (!srv.response.success)
        {
            ROS_WARN_STREAM(apriltag_service_name_ + " call failed, message: " + srv.response.message.c_str());
            return BT::NodeStatus::FAILURE;
        }
        ROS_INFO_STREAM(apriltag_service_name_ + " call success");

        if (!gscam_client_.call(srv))
        {
            gscam_client_ = nh_.serviceClient<std_srvs::SetBool>(gscam_service_name_);
            ROS_WARN_STREAM(gscam_service_name_ + " call failed");
            return BT::NodeStatus::FAILURE;
        }
        if (!srv.response.success)
        {
            ROS_WARN_STREAM(gscam_service_name_ + " call failed, message: " + srv.response.message.c_str());
            return BT::NodeStatus::FAILURE;
        }
        ROS_INFO_STREAM(gscam_service_name_ + " call success");

        return BT::NodeStatus::SUCCESS;
    }

private:
    ros::NodeHandle &nh_;

    std::string gscam_service_name_ = "/publish_image_for_docking";
    ros::ServiceClient gscam_client_;

    std::string apriltag_service_name_ = "/apriltag_ros_continuous_node/enable_apriltag_detection";
    ros::ServiceClient apriltag_client_;
};

class ReduceBackSafety
{
public:
    ReduceBackSafety(ros::NodeHandle &nh)
        : nh_(nh)
    {
        client_ = nh_.serviceClient<std_srvs::SetBool>(service_name_);
    }

    BT::NodeStatus start()
    {
        std_srvs::SetBool srv;
        srv.request.data = true;

        if (!client_.call(srv))
        {
            client_ = nh_.serviceClient<std_srvs::SetBool>(service_name_);
            ROS_WARN_STREAM(service_name_ + " call failed");
            return BT::NodeStatus::FAILURE;
        }
        if (!srv.response.success)
        {
            ROS_WARN_STREAM(service_name_ + " call failed, message: " + srv.response.message.c_str());
            return BT::NodeStatus::FAILURE;
        }

        ROS_INFO_STREAM(service_name_ + " call success");
        return BT::NodeStatus::SUCCESS;
    }

    BT::NodeStatus stop()
    {
        std_srvs::SetBool srv;
        srv.request.data = false;

        if (!client_.call(srv))
        {
            client_ = nh_.serviceClient<std_srvs::SetBool>(service_name_);
            ROS_WARN_STREAM(service_name_ + " call failed");
            return BT::NodeStatus::FAILURE;
        }

        if (!srv.response.success)
        {
            ROS_WARN_STREAM(service_name_ + " call failed, message: " + srv.response.message.c_str());
            return BT::NodeStatus::FAILURE;
        }

        ROS_INFO_STREAM(service_name_ + " call success");
        return BT::NodeStatus::SUCCESS;
    }

private:
    ros::NodeHandle &nh_;
    std::string service_name_ = "/in_docking";
    ros::ServiceClient client_;
};

class DisengageMotors
{
public:
    DisengageMotors(ros::NodeHandle &nh)
        : nh_(nh)
    {
        client_ = nh_.serviceClient<std_srvs::SetBool>(service_name_);
    }

    BT::NodeStatus start()
    {
        std_srvs::SetBool srv;
        srv.request.data = true;

        if (!client_.call(srv))
        {
            client_ = nh_.serviceClient<std_srvs::SetBool>(service_name_);
            ROS_WARN_STREAM(service_name_ + " call failed");
            return BT::NodeStatus::FAILURE;
        }
        if (!srv.response.success)
        {
            ROS_WARN_STREAM(service_name_ + " call failed, message: " + srv.response.message.c_str());
            return BT::NodeStatus::FAILURE;
        }

        ROS_INFO_STREAM(service_name_ + " call success");
        return BT::NodeStatus::SUCCESS;
    }

    BT::NodeStatus stop()
    {
        std_srvs::SetBool srv;
        srv.request.data = false;

        if (!client_.call(srv))
        {
            client_ = nh_.serviceClient<std_srvs::SetBool>(service_name_);
            ROS_WARN_STREAM(service_name_ + " call failed");
            return BT::NodeStatus::FAILURE;
        }

        if (!srv.response.success)
        {
            ROS_WARN_STREAM(service_name_ + " call failed, message: " + srv.response.message.c_str());
            return BT::NodeStatus::FAILURE;
        }

        ROS_INFO_STREAM(service_name_ + " call success");
        return BT::NodeStatus::SUCCESS;
    }

private:
    ros::NodeHandle &nh_;
    std::string service_name_ = "/ottonomy_robot_node/disengage_motors";
    ros::ServiceClient client_;
};

