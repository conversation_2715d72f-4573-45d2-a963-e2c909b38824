# pragma once

#include <ros/ros.h>
#include <ros/package.h>
#include <iostream>
#include <tf2_ros/transform_listener.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/Pose.h>
#include <geometry_msgs/Twist.h>
#include <visualization_msgs/Marker.h>
#include <apriltag_ros/AprilTagDetectionArray.h>
#include <Eigen/Dense>
#include <cmath>
#include <vector>
#include <boost/thread.hpp>

#include <actionlib_msgs/GoalStatusArray.h>
#include <ottonomy_msgs/OttoDockAction.h>
#include <ottonomy_msgs/DockingState.h>
#include <ottonomy_msgs/DockingFeedback.h>
#include <actionlib/server/simple_action_server.h>

#include "behaviortree_cpp/bt_factory.h"
#include "behaviortree_cpp/xml_parsing.h"
#include "otto_docking/behaviortree_simple_nodes.h"
#include "otto_docking/behaviortree_dock_communication_nodes.h"
#include "otto_docking/behaviortree_utilities.h"    

bool load_params();

/**
 * @brief function to calculate yaw error
 *
 * @param pose1 pose from which yaw error is calculated
 * @return double error value
 */
    double calculate_yaw_error(const geometry_msgs::Pose &pose1);

void get_virtual_docking_pose(const geometry_msgs::PoseStamped &pose_in_map_frame,
                              geometry_msgs::PoseStamped &virtual_docking_pose);

/**
 * @brief function to publish visualization maker
 *
 * @param poseStamped pose on which the marker needs to be placed
 * @param ns namespace
 * @param color color of the maker
 * @param size size of the marker
 */
void publish_marker_point(const geometry_msgs::PoseStamped &poseStamped, const std::string &ns, const std::vector<double> &color, double size);

void calc_error_given_target_pose(const geometry_msgs::PoseStamped target_pose, float &error_x, float &error_y, float &error_yaw);

void calc_valid_vel_given_error(float error_x, float error_y, float error_yaw,
                                float &Vx, float &Vy, float &W);

bool check_if_velocities_are_valid(const double V, const double theta, double W_in);

inline void print_and_publish_log(const std::string &msg);

inline void publish_velocity(const float Vx, const float Vy, const float W);

void execute_cb(const ottonomy_msgs::OttoDockGoalConstPtr &otto_docking);

ros::NodeHandle* nh;
ros::Publisher cmd_vel_pub;
ros::Publisher marker_pub;
ros::Publisher docking_state_pub;

tf2_ros::Buffer tf_buffer;
tf2_ros::TransformListener* tf_listener;
geometry_msgs::TransformStamped baseLinkNav_camera_tf;

typedef actionlib::SimpleActionServer<ottonomy_msgs::OttoDockAction> OttoDockActionServer;
OttoDockActionServer *dockAS;
std::string docking_id_;

BT::Blackboard::Ptr global_blackboard_, root_blackboard_;
BT::BehaviorTreeFactory bt_factory_;
ros::Publisher bt_node_transitions_pub_;

// Parameters
bool backwards_docking;
float error_threshold_xy, error_threshold_yaw;
float error_threshold_virtualdocking_xy, error_threshold_virtualdocking_yaw;
float max_linear_velocity_limit, min_linear_velocity_limit, max_angular_velocity_limit, min_angular_velocity_limit;
float kp_x, kp_y, kp_yaw;
bool use_sinh_law, use_tanh_law;
float docking_distance, undocking_distance;
std::string camera_frame;
float max_action_time_sec;
float max_target_lost_time_for_moving_robot;
float target_lost_time_for_recovery;
int num_retries, max_docking_retries;
bool communicate_with_dock_;

