#pragma once

#include "behaviortree_cpp/bt_factory.h"

#include "ros/ros.h"
#include <ottonomy_msgs/RobotIOStates.h>
#include <ottonomy_msgs/RobotInfo.h>
#include <ottonomy_msgs/SetDockingFlags.h>
#include <ottonomy_msgs/MotorDriverFeedback.h>
#include <boost/circular_buffer.hpp>

class DockCommunication
{
public:
    DockCommunication(ros::NodeHandle &nh)
        : nh_(nh)
    {
        if (!load_params())
        {
            ROS_ERROR("Unable to load params for DockCommunication, cannot proceed further!");
            exit(0);
        }

        hardware_feedback_time_ = ros::Time(0);
        communication_flag_status_ = -1;
        charging_flag_status_ = -1;

        contact_force_buffer_size_ = 5;
        contact_force_1_buffer_.resize(contact_force_buffer_size_, 0.0);
        contact_force_2_buffer_.resize(contact_force_buffer_size_, 0.0);

        docking_flags_service_name_ = "/set_docking_flags";
        docking_flags_service_client_ = nh_.serviceClient<ottonomy_msgs::SetDockingFlags>(docking_flags_service_name_);

        robot_io_subscriber_ = nh_.subscribe("/robot_io_states", 1, &DockCommunication::robot_io_subscriber_callback, this);

        last_charge_time_ = ros::Time(0);
        robot_info_subscriber_ = nh_.subscribe("/ottonomy_robot_node/robot_info", 1, &DockCommunication::robot_info_subscriber_callback, this);

        if (use_motor_current_instead_of_contact_force_) // if we want to use motor current instead of contact force
        {
            motor_1_amps_ = 0;
            motor_2_amps_ = 0;
            motor_3_amps_ = 0;
            motor_4_amps_ = 0;

            motor_driver_1_feedback_subscriber_ = nh_.subscribe("/ottonomy_robot_node/R1_motor_driver_feedback", 1, &DockCommunication::motor_driver_1_feedback_callback, this);
            motor_driver_2_feedback_subscriber_ = nh_.subscribe("/ottonomy_robot_node/R2_motor_driver_feedback", 1, &DockCommunication::motor_driver_2_feedback_callback, this);
        }
    }

    BT::NodeStatus start_communication()
    {
        int required_communication_status = 1;
        if (!set_docking_flags(1, required_communication_status))
        {
            return BT::NodeStatus::FAILURE;
        }

        // check if flag has changed
        ros::Time start_time = ros::Time::now();
        while (ros::Time::now().toSec() - start_time.toSec() < 5.0) // TODO is this okay to hold this thread for 5 sec?
        {
            if (communication_flag_status_ == required_communication_status)
            {
                ROS_INFO("hardware start_communication %d success", required_communication_status);
                return BT::NodeStatus::SUCCESS;
            }

            ros::Duration(0.1).sleep();
        }

        int communication_flag_status = communication_flag_status_;
        ROS_WARN("start_communication current state %d required state: %d", communication_flag_status, required_communication_status);
        return BT::NodeStatus::FAILURE;
    }

    BT::NodeStatus stop_communication()
    {
        int required_communication_status = 0;
        if (!set_docking_flags(1, required_communication_status))
        {
            return BT::NodeStatus::FAILURE;
        }

        // check if flag has changed
        ros::Time start_time = ros::Time::now();
        while (ros::Time::now().toSec() - start_time.toSec() < 5.0)
        {
            if (communication_flag_status_ == required_communication_status)
            {
                ROS_INFO("hardware start_communication %d success", required_communication_status);
                return BT::NodeStatus::SUCCESS;
            }

            ros::Duration(0.1).sleep();
        }

        int communication_flag_status = communication_flag_status_;
        ROS_WARN("start_communication current state %d required state: %d", communication_flag_status, required_communication_status);
        return BT::NodeStatus::FAILURE;
    }

    BT::NodeStatus start_charging()
    {
        int required_charging_status = 1;
        if (!set_docking_flags(2, required_charging_status))
        {
            return BT::NodeStatus::FAILURE;
        }

        // check if flag has changed
        ros::Time start_time = ros::Time::now();
        while (ros::Time::now().toSec() - start_time.toSec() < 5.0)
        {
            if (charging_flag_status_ == required_charging_status)
            {
                ROS_INFO("hardware start_charging %d success", required_charging_status);
                return BT::NodeStatus::SUCCESS;
            }

            ros::Duration(0.1).sleep();
        }

        int charging_flag_status = charging_flag_status_;
        ROS_WARN("start_charging current state %d required state: %d", charging_flag_status, required_charging_status);
        return BT::NodeStatus::FAILURE;
    }

    BT::NodeStatus stop_charging()
    {
        int required_charging_status = 0;
        if (!set_docking_flags(2, required_charging_status))
        {
            return BT::NodeStatus::FAILURE;
        }

        // check if flag has changed
        ros::Time start_time = ros::Time::now();
        while (ros::Time::now().toSec() - start_time.toSec() < 5.0)
        {
            if (charging_flag_status_ == required_charging_status)
            {
                ROS_INFO("hardware start_charging %d success", required_charging_status);
                return BT::NodeStatus::SUCCESS;
            }

            ros::Duration(0.1).sleep();
        }

        int charging_flag_status = charging_flag_status_;
        ROS_WARN("start_charging current state %d required state: %d", charging_flag_status, required_charging_status);
        return BT::NodeStatus::FAILURE;
    }

    bool is_contact_force_reached(bool &reached)
    {
        reached = false;

        boost::mutex::scoped_lock l{hardware_feedback_mutex_};
        if (fabs(ros::Time::now().toSec() - hardware_feedback_time_.toSec()) > 0.1)
        {
            ROS_ERROR("Contact force data too old!!");
            reached = false;
            return false;
        }

        if (((contact_force_1_average_ > contact_force_threshold_) || (contact_force_2_average_ > contact_force_threshold_)) &&
            ((contact_force_1_average_ > min_contact_force_threshold_) && (contact_force_2_average_ > min_contact_force_threshold_)))
        {
            ROS_INFO_STREAM("Contact force > threshold " +
                            std::to_string(contact_force_1_average_) +
                            " " + std::to_string(contact_force_2_average_));
            reached = true;
        }

        return true;
    }

    bool is_motor_amps_reached(bool &reached)
    {
        reached = false;
        float motor_amps_total = 0.0;

        {
            boost::mutex::scoped_lock l{motor_driver_1_feedback_mutex_};

            if (fabs(ros::Time::now().toSec() - motor_driver_1_feedback_time_.toSec()) > 0.1)
            {
                ROS_ERROR("Motor 1 amps data too old!!");
                return false;
            }

            motor_amps_total += motor_1_amps_;
            motor_amps_total += motor_2_amps_;
        }

        {
            boost::mutex::scoped_lock l{motor_driver_2_feedback_mutex_};

            if (fabs(ros::Time::now().toSec() - motor_driver_2_feedback_time_.toSec()) > 0.1)
            {
                ROS_ERROR("Motor 2 amps data too old!!");
                return false;
            }

            motor_amps_total += motor_3_amps_;
            motor_amps_total += motor_4_amps_;
        }

        if (motor_amps_total > motor_amps_threshold_)
        {
            ROS_INFO("Motor current %f > threshold %f", motor_amps_total, motor_amps_threshold_);
            reached = true;
        }

        return true;
    }

    BT::NodeStatus is_robot_charging()
    {
        boost::mutex::scoped_lock l{robot_info_mutex_};

        if (fabs(ros::Time::now().toSec() - last_charge_time_.toSec()) < 50.0)
        {
            return BT::NodeStatus::SUCCESS;
        }
        else
        {
            return BT::NodeStatus::FAILURE;
        }
    }

private:
    bool load_params()
    {
        ros::NodeHandle private_nh("~");

        if (!private_nh.getParam("dock_name", dock_name_))
        {
            dock_name_ = "DOCK_BACK";
            ROS_WARN("Defaulting dock_name param to %s", dock_name_.c_str());
        }

        if (!private_nh.getParam("contact_force_threshold", contact_force_threshold_))
        {
            contact_force_threshold_ = 500;
            ROS_WARN("Defaulting contact_force_threshold param to %d", contact_force_threshold_);
        }

        if (!private_nh.getParam("min_contact_force_threshold", min_contact_force_threshold_))
        {
            min_contact_force_threshold_ = 50;
            ROS_WARN("Defaulting min_contact_force_threshold param to %d", min_contact_force_threshold_);
        }

        if (!private_nh.getParam("contact_force_buffer_size", contact_force_buffer_size_))
        {
            contact_force_buffer_size_ = 5;
            ROS_WARN("Defaulting contact_force_buffer_size param to %d", contact_force_buffer_size_);
        }

        if (!private_nh.getParam("use_motor_current_instead_of_contact_force", use_motor_current_instead_of_contact_force_))
        {
            use_motor_current_instead_of_contact_force_ = false;
            ROS_WARN("Defaulting use_motor_current_instead_of_contact_force param to %d", use_motor_current_instead_of_contact_force_);
        }

        if (!private_nh.getParam("motor_amps_threshold", motor_amps_threshold_))
        {
            motor_amps_threshold_ = 30.0;
            ROS_WARN("Defaulting motor_amps_threshold param to %f", motor_amps_threshold_);
        }

        return true;
    }

    void robot_io_subscriber_callback(const ottonomy_msgs::RobotIOStates &robot_io_states)
    {
        boost::mutex::scoped_lock l{hardware_feedback_mutex_};

        hardware_feedback_time_ = robot_io_states.docking[0].stamp;

        contact_force_1_buffer_.push_back((int)robot_io_states.docking[0].contact_force1);
        contact_force_2_buffer_.push_back((int)robot_io_states.docking[0].contact_force2);

        contact_force_1_average_ = std::accumulate(contact_force_1_buffer_.begin(), contact_force_1_buffer_.end(), 0.0) / contact_force_1_buffer_.size();
        contact_force_2_average_ = std::accumulate(contact_force_2_buffer_.begin(), contact_force_2_buffer_.end(), 0.0) / contact_force_2_buffer_.size();

        // TODO - use dock_name_ variable here? do we have multiple docks?
        communication_flag_status_ = robot_io_states.docking[0].communication_status;
        charging_flag_status_ = robot_io_states.docking[0].charging_status;
    }

    void robot_info_subscriber_callback(const ottonomy_msgs::RobotInfo &robot_info)
    {
        boost::mutex::scoped_lock l{robot_info_mutex_};

        if (robot_info.status == "CHARGING" || robot_info.status == "CHARGER_CONNECTED")
        {
            last_charge_time_ = ros::Time::now();
        }
    }

    bool set_docking_flags(int flag_num, int flag_val)
    {
        ottonomy_msgs::SetDockingFlags srv;
        srv.request.name = dock_name_;
        srv.request.flag_num = flag_num;
        srv.request.flag_val = flag_val;

        if (!docking_flags_service_client_.call(srv))
        {
            docking_flags_service_client_ = nh_.serviceClient<ottonomy_msgs::SetDockingFlags>(docking_flags_service_name_);
            ROS_WARN_STREAM(docking_flags_service_name_ + " call failed");
            return false;
        }
        if (!srv.response.success)
        {
            ROS_WARN_STREAM(docking_flags_service_name_ + " success false");
            return false;
        }

        ROS_INFO_STREAM(docking_flags_service_name_ + " call success");
        return true;
    }

    void motor_driver_1_feedback_callback(const ottonomy_msgs::MotorDriverFeedback &feedback)
    {
        boost::mutex::scoped_lock l{motor_driver_1_feedback_mutex_};
        motor_driver_1_feedback_time_ = feedback.header.stamp;
        motor_1_amps_ = feedback.current[0];
        motor_2_amps_ = feedback.current[1];
    }

    void motor_driver_2_feedback_callback(const ottonomy_msgs::MotorDriverFeedback &feedback)
    {
        boost::mutex::scoped_lock l{motor_driver_2_feedback_mutex_};
        motor_driver_2_feedback_time_ = feedback.header.stamp;
        motor_3_amps_ = feedback.current[0];
        motor_4_amps_ = feedback.current[1];
    }

    ros::NodeHandle &nh_;

    // parameters
    std::string dock_name_;
    int contact_force_threshold_;
    int min_contact_force_threshold_;
    bool use_motor_current_instead_of_contact_force_;
    float motor_amps_threshold_;

    // for contact force feedback
    ros::Subscriber robot_io_subscriber_;
    boost::mutex hardware_feedback_mutex_;
    int contact_force_buffer_size_;
    boost::circular_buffer<int> contact_force_1_buffer_, contact_force_2_buffer_;
    float contact_force_1_average_, contact_force_2_average_;
    std::atomic_int16_t communication_flag_status_, charging_flag_status_;
    ros::Time hardware_feedback_time_;

    // set docking flags
    std::string docking_flags_service_name_ = "/set_docking_flags";
    ros::ServiceClient docking_flags_service_client_;

    // charge monitoring
    boost::mutex robot_info_mutex_;
    ros::Time last_charge_time_;
    ros::Subscriber robot_info_subscriber_;

    // for motor current feedback
    ros::Subscriber motor_driver_1_feedback_subscriber_, motor_driver_2_feedback_subscriber_;
    boost::mutex motor_driver_1_feedback_mutex_, motor_driver_2_feedback_mutex_;
    ros::Time motor_driver_1_feedback_time_, motor_driver_2_feedback_time_;
    float motor_1_amps_, motor_2_amps_, motor_3_amps_, motor_4_amps_;
};
