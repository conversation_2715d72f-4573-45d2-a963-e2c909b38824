#include "otto_docking/dock_behavior_node.h"

bool load_params()
{
    ros::NodeHandle private_nh("~");

    if (!private_nh.getParam("camera_frame", camera_frame))
    {
        ROS_ERROR("Unable to load camera_frame param");
        return false;
    }

    if (!private_nh.getParam("docking_distance", docking_distance))
    {
        ROS_ERROR("Unable to load docking_distance param");
        return false;
    }

    if (!private_nh.getParam("undocking_distance", undocking_distance))
    {
        ROS_ERROR("Unable to load undocking_distance param");
        return false;
    }

    if (!private_nh.getParam("enable_backwards_docking", backwards_docking))
    {
        ROS_ERROR("Unable to load enable_backwards_docking param");
        return false;
    }

    if (!private_nh.getParam("error_threshold_xy", error_threshold_xy))
    {
        error_threshold_xy = 0.03;
        ROS_WARN("Defaulting error_threshold_xy param to %lf", error_threshold_xy);
    }

    if (!private_nh.getParam("error_threshold_yaw", error_threshold_yaw))
    {
        error_threshold_yaw = 0.04;
        ROS_WARN("Defaulting error_threshold_yaw param to %lf", error_threshold_yaw);
    }

    if (!private_nh.getParam("error_threshold_virtualdocking_xy", error_threshold_virtualdocking_xy))
    {
        error_threshold_virtualdocking_xy = 0.15;
        ROS_WARN("Defaulting error_threshold_virtualdocking_xy param to %lf", error_threshold_virtualdocking_xy);
    }

    if (!private_nh.getParam("error_threshold_virtualdocking_yaw", error_threshold_virtualdocking_yaw))
    {
        error_threshold_virtualdocking_yaw = 0.15;
        ROS_WARN("Defaulting error_threshold_virtualdocking_yaw param to %lf", error_threshold_virtualdocking_yaw);
    }

    if (!private_nh.getParam("max_linear_velocity_limit", max_linear_velocity_limit))
    {
        max_linear_velocity_limit = 0.2;
        ROS_WARN("Defaulting max_linear_velocity_limit param to %lf", max_linear_velocity_limit);
    }

    if (!private_nh.getParam("min_linear_velocity_limit", min_linear_velocity_limit))
    {
        min_linear_velocity_limit = 0.06;
        ROS_WARN("Defaulting min_linear_velocity_limit param to %lf", min_linear_velocity_limit);
    }

    if (!private_nh.getParam("max_angular_velocity_limit", max_angular_velocity_limit))
    {
        max_angular_velocity_limit = 0.2;
        ROS_WARN("Defaulting max_angular_velocity_limit param to %lf", max_angular_velocity_limit);
    }

    if (!private_nh.getParam("min_angular_velocity_limit", min_angular_velocity_limit))
    {
        min_angular_velocity_limit = 0.05;
        ROS_WARN("Defaulting min_angular_velocity_limit param to %lf", min_angular_velocity_limit);
    }

    if (!private_nh.getParam("max_action_time_sec", max_action_time_sec))
    {
        max_action_time_sec = 30.0;
        ROS_WARN("Defaulting min_angular_velocity_limit param to %lf", max_action_time_sec);
    }

    if (!private_nh.getParam("max_target_lost_time_for_moving_robot", max_target_lost_time_for_moving_robot))
    {
        max_target_lost_time_for_moving_robot = 0.6;
        ROS_WARN("Defaulting max_target_lost_time_for_moving_robot param to %lf", max_target_lost_time_for_moving_robot);
    }

    if (!private_nh.getParam("target_lost_time_for_recovery", target_lost_time_for_recovery))
    {
        target_lost_time_for_recovery = 5.0;
        ROS_WARN("Defaulting target_lost_time_for_recovery param to %lf", target_lost_time_for_recovery);
    }

    if (!private_nh.getParam("max_docking_retries", max_docking_retries))
    {
        max_docking_retries = 5;
        ROS_WARN("Defaulting max_docking_retries param to %d", max_docking_retries);
    }

    if (!private_nh.getParam("communicate_with_dock", communicate_with_dock_))
    {
        communicate_with_dock_ = false;
        ROS_WARN("Defaulting communicate_with_dock param to %d", communicate_with_dock_);
    }

    if (!private_nh.getParam("kp_x", kp_x))
    {
        kp_x = 0.8;
        ROS_WARN("Defaulting kp_x param to %lf", kp_x);
    }

    if (!private_nh.getParam("kp_y", kp_y))
    {
        kp_y = 0.05;
        ROS_WARN("Defaulting kp_y param to %lf", kp_y);
    }

    if (!private_nh.getParam("kp_yaw", kp_yaw))
    {
        kp_yaw = 0.2;
        ROS_WARN("Defaulting kp_yaw param to %lf", kp_yaw);
    }

    if (!private_nh.getParam("use_sinh_law", use_sinh_law))
    {
        use_sinh_law = true;
        ROS_WARN("Defaulting use_sinh_law param to %d", use_sinh_law);
    }

    if (!private_nh.getParam("use_tanh_law", use_tanh_law))
    {
        use_tanh_law = true;
        ROS_WARN("Defaulting use_tanh_law param to %d", use_tanh_law);
    }

    return true;
}

void publish_marker_point(const geometry_msgs::PoseStamped &poseStamped, const std::string &ns, const std::vector<double> &color, double size)
{
    visualization_msgs::Marker marker;
    marker.header = poseStamped.header;
    marker.ns = ns;
    marker.id = 0;
    marker.type = visualization_msgs::Marker::ARROW; // Use ARROW type for visualization of orientation
    marker.action = visualization_msgs::Marker::ADD;
    marker.pose = poseStamped.pose;
    marker.scale.x = size * 2.0; // Adjust the scale for the arrow length
    marker.scale.y = size / 2.0; // Adjust the scale for the arrow width
    marker.scale.z = size / 2.0; // Adjust the scale for the arrow height
    marker.color.r = color[0];
    marker.color.g = color[1];
    marker.color.b = color[2];
    marker.color.a = color[3];
    marker.lifetime = ros::Duration(1); // Adjust the lifetime as needed

    // Use a Publisher to publish the marker
    marker_pub.publish(marker);
}

void get_virtual_docking_pose(const geometry_msgs::PoseStamped &pose_in_map_frame,
                              geometry_msgs::PoseStamped &virtual_docking_pose)
{
    // convert pose from map frame to base_link_nav frame
    geometry_msgs::TransformStamped map_baseLinkNav_tf;
    bool got_map_baseLinkNav_tf = false;
    while (!got_map_baseLinkNav_tf)
    {
        try
        {
            map_baseLinkNav_tf = tf_buffer.lookupTransform("base_link_nav", pose_in_map_frame.header.frame_id, ros::Time(0));
            got_map_baseLinkNav_tf = true;
        }
        catch (tf2::TransformException &ex)
        {
            ROS_WARN("Error getting tf from base_link_nav to camera frame, error %s", ex.what());
            ros::Duration(0.02).sleep();
        }
    }

    tf2::doTransform(pose_in_map_frame, virtual_docking_pose, map_baseLinkNav_tf);
    publish_marker_point(virtual_docking_pose, "virtual_docking_pose", {1.0, 1.0, 0.0, 1.0}, 0.1);
}

double calculate_yaw_error(const geometry_msgs::Pose &pose1)
{
    tf2::Quaternion quaternion1(pose1.orientation.x, pose1.orientation.y, pose1.orientation.z, pose1.orientation.w);
    tf2::Matrix3x3 mat(quaternion1);
    double roll, pitch, yaw;
    mat.getRPY(roll, pitch, yaw);
    return yaw;
}

void calc_error_given_target_pose(const geometry_msgs::PoseStamped target_pose,
                                  float &error_x, float &error_y, float &error_yaw)
{
    double backwards_docking_factor = (backwards_docking) ? -1.0 : 1.0;

    error_x = target_pose.pose.position.x * backwards_docking_factor;
    error_y = target_pose.pose.position.y;
    error_yaw = calculate_yaw_error(target_pose.pose);

    publish_marker_point(target_pose, "docking_marker", {1.0, 0.0, 0.0, 1.0}, 0.1);
}

void calc_valid_vel_given_error(float error_x, float error_y, float error_yaw,
                                float &Vx, float &Vy, float &W)
{
    bool valid_velocities_generated = false;
    float backwards_docking_factor = (backwards_docking) ? -1.0 : 1.0;

    while (!valid_velocities_generated)
    {
        // calculate velocities for the error
        if (use_sinh_law)
        {
            Vx = max_linear_velocity_limit * sinh(kp_x * error_x / max_linear_velocity_limit) * backwards_docking_factor;
            Vy = max_linear_velocity_limit * sinh(kp_y * error_y / max_linear_velocity_limit);
            W = max_angular_velocity_limit * sinh(kp_yaw * error_yaw / max_angular_velocity_limit);
        }
        else if (use_tanh_law)
        {
            Vx = max_linear_velocity_limit * tanh(kp_x * error_x / max_linear_velocity_limit) * backwards_docking_factor;
            Vy = max_linear_velocity_limit * tanh(kp_y * error_y / max_linear_velocity_limit);
            W = max_angular_velocity_limit * tanh(kp_yaw * error_yaw / max_angular_velocity_limit);
        }
        else
        {
            Vx = kp_x * error_x;
            Vy = kp_y * error_y;
            W = kp_yaw * error_yaw;
        }

        // apply velocity limits on velocities generated
        if (Vx > 0)
        {
            Vx = std::min(Vx, max_linear_velocity_limit);
            Vx = std::max(Vx, min_linear_velocity_limit);
        }
        else if (Vx < 0)
        {
            Vx = std::max(Vx, -max_linear_velocity_limit);
            Vx = std::min(Vx, -min_linear_velocity_limit);
        }
        else
        {
            Vx = 0.0;
        }

        if (Vy > 0)
        {
            Vy = std::min(Vy, max_linear_velocity_limit);
            // Vy = std::max(Vy, min_linear_velocity_limit);
            // if (fabs(Vy) < min_linear_velocity_limit)
            //     Vy = 0.0;
        }
        else if (Vy < 0)
        {
            Vy = std::max(Vy, -max_linear_velocity_limit);
            // Vy = std::min(Vy, -min_linear_velocity_limit);
            // if (fabs(Vy) < min_linear_velocity_limit)
            //     Vy = 0.0;
        }
        else
        {
            Vy = 0.0;
        }

        if (W > 0)
        {
            W = std::min(W, max_angular_velocity_limit);
            // W = std::max(W, min_angular_velocity_limit);
            // if (fabs(W) < min_angular_velocity_limit)
            //     W = 0.0;
        }
        else if (W < 0)
        {
            W = std::max(W, -max_angular_velocity_limit);
            // W = std::min(W, -min_angular_velocity_limit);
            // if (fabs(W) < min_angular_velocity_limit)
            //     W = 0.0;
        }
        else
        {
            W = 0.0;
        }

        // check if velocities generated are valid
        bool valid_velocities_generated = check_if_velocities_are_valid(Vx, Vy, W);
        if (valid_velocities_generated)
        {
            valid_velocities_generated = true;
            break;
        }
        else
        {
            error_y *= 0.9;
            error_yaw *= 0.9;
        }
    }
}

bool check_if_velocities_are_valid(const double V, const double theta, double W_in)
{
    bool straight_steering_when_stopping_ = true;
    double steer_max_cmd = 1800;
    double position_scaling_factor = 4864;
    double max_steer_angle = steer_max_cmd * M_PI / position_scaling_factor;
    double ROBOT_LENGTH = 0.685;
    double ROBOT_WIDTH = 0.425;
    double FRONT_OFFSET_DISTANCE = 0.070;
    double Rx_in = ROBOT_LENGTH / 2.0;
    double Ry_in = ROBOT_WIDTH / 2.0;
    double OD_in = FRONT_OFFSET_DISTANCE;
    double vel_pos[8] = {0.0};
    int S_FL = 0, V_FL = 1, S_FR = 2, V_FR = 3, S_RL = 4, V_RL = 5, S_RR = 6, V_RR = 7;

    double Vx_in = V;
    double Vy_in = theta;
    if (V < 0.0)
    {
        W_in *= -1;
        Vy_in *= -1;
    }

    double Vx, Vy, Rx, Ry;
    int dir = Vx_in / fabs(Vx_in);
    Vx = dir * Vx_in - W_in * Ry_in;
    Vy = Vy_in + W_in * Rx_in;
    vel_pos[S_FL] = atan2(Vy, Vx);

    Vx = dir * Vx_in + W_in * Ry_in;
    Vy = Vy_in + W_in * Rx_in;
    vel_pos[S_FR] = atan2(Vy, Vx);

    Vx = dir * Vx_in - W_in * Ry_in;
    Vy = Vy_in - W_in * Rx_in;
    vel_pos[S_RL] = atan2(Vy, Vx);

    Vx = dir * Vx_in + W_in * Ry_in;
    Vy = Vy_in - W_in * Rx_in;
    vel_pos[S_RR] = atan2(Vy, Vx);

    if (fabs(vel_pos[S_FL]) < max_steer_angle && fabs(vel_pos[S_FR]) < max_steer_angle &&
        fabs(vel_pos[S_RL]) < max_steer_angle && fabs(vel_pos[S_RR]) < max_steer_angle)
    {
        return true;
    }
    else
    {
        // ROS_WARN("Invalid input commands, swerve steering command out of range");
        return false;
    }
}

inline void print_and_publish_log(const std::string &msg)
{
    ROS_INFO("%s", msg.c_str());

    // ottonomy_msgs::OttoDockFeedback feedback;
    // feedback.id = docking_id;
    // feedback.status = msg;
    // dockAS->publishFeedback(feedback);
}

inline void publish_velocity(const float Vx, const float Vy, const float W)
{
    geometry_msgs::Twist cmd_vel;
    cmd_vel.linear.x = Vx;
    cmd_vel.linear.y = Vy;
    cmd_vel.angular.z = W;
    cmd_vel_pub.publish(cmd_vel);
}

class CheckIfMapPoseIsTooFar : public BT::SyncActionNode
{
public:
    CheckIfMapPoseIsTooFar(const std::string &name, const BT::NodeConfig &config)
        : BT::SyncActionNode(name, config)
    {
    }

    static BT::PortsList providedPorts()
    {
        return {BT::InputPort<geometry_msgs::PoseStamped>("target_pose")};
    }

    BT::NodeStatus tick() override
    {
        // check if the input ports have received
        auto res = getInput<geometry_msgs::PoseStamped>("target_pose");
        if (!res)
        {
            ROS_ERROR_STREAM("error reading port [target_pose]:" + res.error());
            throw BT::RuntimeError("error reading port [target_pose]:", res.error());
        }
        target_pose_ = res.value();

        // get the virtual docking pose in base link nav frame
        geometry_msgs::PoseStamped target_pose_baselinknav_frame;
        get_virtual_docking_pose(target_pose_, target_pose_baselinknav_frame);

        // calculate the error for the given target pose
        float error_x, error_y, error_yaw;
        calc_error_given_target_pose(target_pose_baselinknav_frame, error_x, error_y, error_yaw);

        // reject goal if virtual docking pose is too far away from robot location
        if (fabs(error_x) > 3.0 || fabs(error_y) > 3.0 || fabs(error_yaw) > 1.5)
        {
            ROS_ERROR_STREAM("Rejecting the goal since error in virtual docking too much, error_x, error_y, error_yaw: " +
                             std::to_string(error_x) + " " +
                             std::to_string(error_y) + " " +
                             std::to_string(error_yaw));

            return BT::NodeStatus::SUCCESS;
        }

        return BT::NodeStatus::FAILURE;
    }

private:
    geometry_msgs::PoseStamped target_pose_;
};

class MoveToMapPose : public BT::StatefulActionNode
{
public:
    MoveToMapPose(const std::string &name, const BT::NodeConfig &config)
        : BT::StatefulActionNode(name, config), halted_(false), target_reached_(false)
    {
    }

    static BT::PortsList providedPorts()
    {
        return {BT::InputPort<geometry_msgs::PoseStamped>("target_pose")};
    }

    BT::NodeStatus onStart() override
    {
        // check if the input ports have received
        auto res = getInput<geometry_msgs::PoseStamped>("target_pose");
        if (!res)
        {
            ROS_ERROR_STREAM("error reading port [target_pose]:" + res.error());
            throw BT::RuntimeError("error reading port [target_pose]:", res.error());
        }
        target_pose_ = res.value();

        // start the run thread to move the robot
        halted_ = false;
        target_reached_ = false;
        thread_ = std::thread(&MoveToMapPose::runThread, this);

        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus onRunning() override
    {
        if (halted_)
        {
            if (thread_.joinable())
                thread_.join();

            return BT::NodeStatus::FAILURE;
        }
        else if (target_reached_)
        {
            if (thread_.joinable())
                thread_.join();

            return BT::NodeStatus::SUCCESS;
        }

        return BT::NodeStatus::RUNNING;
    }

    void onHalted() override
    {
        halted_ = true;

        if (thread_.joinable())
        {
            thread_.join();
        }
    }

    void runThread()
    {
        ros::Rate rate(20.0);
        ros::Time start_time = ros::Time::now();
        ROS_INFO("Started MoveToMapPose");
        while (true)
        {
            // check if halt is requested
            if (halted_)
            {
                ROS_WARN("MoveToMapPose halt requested");
                break;
            }

            // check if time limit exceeded
            if (fabs(ros::Time::now().toSec() - start_time.toSec()) > max_action_time_sec)
            {
                ROS_WARN("MoveToMapPose time limit exceeded");
                halted_ = true;
                break;
            }

            // get the virtual docking pose in base link nav frame
            geometry_msgs::PoseStamped target_pose_baselinknav_frame;
            get_virtual_docking_pose(target_pose_, target_pose_baselinknav_frame);

            // calculate the error for the given target pose
            float error_x, error_y, error_yaw;
            calc_error_given_target_pose(target_pose_baselinknav_frame, error_x, error_y, error_yaw);

            // check if target is reached
            if (fabs(error_x) < error_threshold_virtualdocking_xy &&
                fabs(error_y) < error_threshold_virtualdocking_xy &&
                fabs(error_yaw) < error_threshold_virtualdocking_yaw)
            {
                ROS_INFO_STREAM("MoveToMapPose target reached error_x: " + std::to_string(error_x) +
                                " error_y: " + std::to_string(error_y) +
                                " error_yaw: " + std::to_string(error_yaw));

                // stop the robot
                publish_velocity(0.0, 0.0, 0.0);

                target_reached_ = true;
                break;
            }

            // compute valid velocity and move the robot to target location
            float Vx, Vy, W;
            calc_valid_vel_given_error(error_x, error_y, error_yaw, Vx, Vy, W);
            publish_velocity(Vx, Vy, W);

            ottonomy_msgs::DockingState docking_state;
            docking_state.error_x = error_x;
            docking_state.error_y = error_y;
            docking_state.error_yaw = error_yaw;
            docking_state.vel_lin_x = Vx;
            docking_state.delay_in_processing_pose = 0.0;
            docking_state_pub.publish(docking_state);

            rate.sleep();
        }

        ROS_INFO("exiting MoveToMapPose");

        publish_velocity(0.0, 0.0, 0.0);
        return;
    }

private:
    geometry_msgs::PoseStamped target_pose_;
    std::atomic_bool halted_;
    std::atomic_bool target_reached_;
    std::thread thread_;
};

class MoveToApriltagPose : public BT::StatefulActionNode
{
public:
    MoveToApriltagPose(const std::string &name, const BT::NodeConfig &config)
        : BT::StatefulActionNode(name, config), halted_(false), target_reached_(false)
    {
        ros::NodeHandle private_nh("~");
        target_detection_time_ = ros::Time(0);
        target_pose_subscriber_ = private_nh.subscribe("/tag_detections", 1, &MoveToApriltagPose::detection_callback, this);
    }

    static BT::PortsList providedPorts()
    {
        return {BT::InputPort<bool>("move_only_in_x"),
                BT::InputPort<float>("x_offset"),
                BT::InputPort<float>("y_offset")};
    }

    BT::NodeStatus onStart() override
    {
        // check if the input ports have received
        auto move_only_in_x = getInput<bool>("move_only_in_x");
        if (!move_only_in_x)
        {
            ROS_ERROR_STREAM("error reading port [move_only_in_x]:" + move_only_in_x.error());
            throw BT::RuntimeError("error reading port [move_only_in_x]:", move_only_in_x.error());
        }
        move_only_in_x_ = move_only_in_x.value();

        auto x_offset = getInput<bool>("x_offset");
        if (!x_offset)
        {
            ROS_ERROR_STREAM("error reading port [x_offset]:" + x_offset.error());
            throw BT::RuntimeError("error reading port [x_offset]:", x_offset.error());
        }
        x_offset_distance_ = x_offset.value();

        auto y_offset = getInput<bool>("y_offset");
        if (!y_offset)
        {
            ROS_ERROR_STREAM("error reading port [y_offset]:" + y_offset.error());
            throw BT::RuntimeError("error reading port [y_offset]:", y_offset.error());
        }
        y_offset_distance_ = y_offset.value();

        halted_ = false;
        target_reached_ = false;
        thread_ = std::thread(&MoveToApriltagPose::runThread, this);

        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus onRunning() override
    {
        if (halted_)
        {
            if (thread_.joinable())
                thread_.join();

            return BT::NodeStatus::FAILURE;
        }
        else if (target_reached_)
        {
            if (thread_.joinable())
                thread_.join();

            return BT::NodeStatus::SUCCESS;
        }

        return BT::NodeStatus::RUNNING;
    }

    void onHalted() override
    {
        halted_ = true;

        if (thread_.joinable())
        {
            thread_.join();
        }
    }

    void runThread()
    {
        ros::Rate rate(20.0);
        ros::Time start_time = ros::Time::now();
        while (true)
        {
            ottonomy_msgs::DockingState docking_state;

            // check if halt is requested
            if (halted_)
            {
                break;
            }

            // check if time limit exceeded
            if (fabs(ros::Time::now().toSec() - start_time.toSec()) > max_action_time_sec)
            {
                ROS_WARN("Exiting MoveToApriltagPose since time limit exceeded");
                halted_ = true;
                break;
            }

            // calculate the error for the given target pose
            float error_x, error_y, error_yaw;
            float delay_in_processing_pose;
            {
                boost::mutex::scoped_lock l{target_pose_mutex_};
                calc_error_given_target_pose(target_pose_, error_x, error_y, error_yaw);
                delay_in_processing_pose = fabs(ros::Time::now().toSec() - target_detection_time_.toSec());
            }

            // check if target is lost
            if (delay_in_processing_pose > target_lost_time_for_recovery)
            {
                ROS_WARN_STREAM("exiting MoveToApriltagPose since too much delay [" +
                                std::to_string(delay_in_processing_pose) + "] in processing pose");

                halted_ = true;
                break;
            }
            else
            {
                ROS_WARN_STREAM("stopping the robot since too much delay [" +
                                std::to_string(delay_in_processing_pose) + "] in processing pose");

                publish_velocity(0.0, 0.0, 0.0);

                docking_state.delay_in_processing_pose = delay_in_processing_pose;
                docking_state_pub.publish(docking_state);

                rate.sleep();
                continue;
            }

            // check if target is reached
            bool reached = false;
            if (move_only_in_x_ && fabs(error_x) < error_threshold_xy)
            {
                reached = true;
            }
            else if (fabs(error_x) < error_threshold_xy &&
                     fabs(error_y) < error_threshold_xy &&
                     fabs(error_yaw) < error_threshold_yaw)
            {
                reached = true;
            }

            if (reached)
            {
                ROS_INFO_STREAM("MoveToVirtualPose target reached error_x: " + std::to_string(error_x) +
                                " error_y: " + std::to_string(error_y) +
                                " error_yaw: " + std::to_string(error_yaw));

                // stop the robot
                publish_velocity(0.0, 0.0, 0.0);

                target_reached_ = true;
                break;
            }

            // compute valid velocity and move the robot to target location
            float Vx, Vy, W;
            calc_valid_vel_given_error(error_x, error_y, error_yaw, Vx, Vy, W);

            docking_state.error_x = error_x;
            docking_state.error_y = error_y;
            docking_state.error_yaw = error_yaw;
            if (move_only_in_x_)
            {
                publish_velocity(Vx, 0.0, 0.0);
                docking_state.vel_lin_x = Vx;
                docking_state.vel_lin_y = 0.0;
                docking_state.vel_ang_z = 0.0;
            }
            else
            {
                publish_velocity(Vx, Vy, W);
                docking_state.vel_lin_x = Vx;
                docking_state.vel_lin_y = Vy;
                docking_state.vel_ang_z = W;
            }

            docking_state.delay_in_processing_pose = delay_in_processing_pose;
            docking_state_pub.publish(docking_state);

            rate.sleep();
        }

        publish_velocity(0.0, 0.0, 0.0);
        return;
    }

    void detection_callback(const apriltag_ros::AprilTagDetectionArray &marker)
    {
        if (marker.detections.size() != 2)
        {
            ROS_INFO_THROTTLE(3, "Need exactly 2 April Tags for docking!");
            return;
        }

        {
            boost::mutex::scoped_lock l{target_pose_mutex_};
            target_detection_time_ = marker.header.stamp;
            tag_1_point_ = marker.detections[0].pose.pose.pose.position;
            tag_2_point_ = marker.detections[1].pose.pose.pose.position;

            get_target_pose();
        }
    }

    void get_target_pose()
    {
        // Convert positions to Eigen vectors
        double offset = backwards_docking ? y_offset_distance_ : -y_offset_distance_;
        Eigen::Vector3d pose1_array(tag_1_point_.x + y_offset_distance_, 0.0, tag_1_point_.z);
        Eigen::Vector3d pose2_array(tag_2_point_.x + y_offset_distance_, 0.0, tag_2_point_.z);

        // Calculate LineVector
        Eigen::Vector3d line_vector = pose2_array - pose1_array;

        // Calculate Midpoint
        Eigen::Vector3d midpoint_array = (pose1_array + pose2_array) / 2.0;

        // Calculate Perpendicular Vector
        Eigen::Vector3d up_vector(0, -1, 0);
        Eigen::Vector3d perpendicular_vector = line_vector.cross(up_vector);

        // Normalize Perpendicular Vector
        perpendicular_vector.normalize();

        // Calculate Roll, Pitch, and Yaw angles based on the perpendicular vector
        double roll = 0.0; // Assuming no roll
        double pitch = std::asin(-perpendicular_vector[1]);
        double yaw = std::atan2(perpendicular_vector[2], perpendicular_vector[0]);

        // Apply 180-degree rotation if backwards_docking is enabled
        if (!backwards_docking)
        {
            double current_yaw = std::atan2(perpendicular_vector[2], -perpendicular_vector[0]);
            yaw += M_PI; // Add 180 degrees to the yaw angle
        }

        // Translate the midpoint 1 meter forward docking_distance
        Eigen::Vector3d translated_point_1 = midpoint_array + x_offset_distance_ * perpendicular_vector;

        // Create a new PoseStamped for the projected point
        geometry_msgs::PoseStamped projected_pose;
        projected_pose.pose.position.x = translated_point_1[0];
        projected_pose.pose.position.y = translated_point_1[1];
        projected_pose.pose.position.z = translated_point_1[2];

        // Set orientation based on roll, pitch, and yaw
        tf2::Quaternion orientation_quaternion;
        orientation_quaternion.setRPY(roll, -yaw, pitch);
        projected_pose.pose.orientation = tf2::toMsg(orientation_quaternion);

        tf2::doTransform(projected_pose, target_pose_, baseLinkNav_camera_tf);
        publish_marker_point(target_pose_, "target_pose", {1.0, 1.0, 0.0, 1.0}, 0.1);
    }

private:
    boost::mutex target_pose_mutex_;
    ros::Time target_detection_time_;
    ros::Subscriber target_pose_subscriber_;
    geometry_msgs::Point tag_1_point_, tag_2_point_;
    geometry_msgs::PoseStamped target_pose_;

    bool move_only_in_x_;
    float x_offset_distance_;
    float y_offset_distance_;

    std::atomic_bool halted_;
    std::atomic_bool target_reached_;
    std::thread thread_;
};

BT::NodeStatus IsPreemptRequested()
{
    if (dockAS->isPreemptRequested())
    {
        return BT::NodeStatus::SUCCESS;
    }

    return BT::NodeStatus::FAILURE;
}

BT::NodeStatus PremptActionGoal()
{
    ottonomy_msgs::OttoDockResult result;
    result.id = docking_id_;
    result.action_completed = false;
    dockAS->setPreempted(result, "Docking prempted");

    return BT::NodeStatus::SUCCESS;
}

BT::NodeStatus AbortActionGoal()
{
    ottonomy_msgs::OttoDockResult result;
    result.id = docking_id_;
    result.action_completed = false;
    dockAS->setAborted(result, "Docking aborted");

    return BT::NodeStatus::SUCCESS;
}

BT::NodeStatus SucceededActionGoal()
{
    ottonomy_msgs::OttoDockResult result;
    result.id = docking_id_;
    result.action_completed = false;
    dockAS->setSucceeded(result, "Docking succeeded");

    return BT::NodeStatus::SUCCESS;
}

void execute_cb(const ottonomy_msgs::OttoDockGoalConstPtr &otto_docking)
{
    ROS_INFO("In execute_cb, received a new docking goal \ngoal_type: %s \ngoal_id: %s \nstation_id: %s",
             otto_docking->type.c_str(), otto_docking->id.c_str(), otto_docking->station_id.c_str());

    docking_id_ = otto_docking->id;

    std::string otto_docking_package_path = ros::package::getPath("otto_docking");
    std::string tree_definition_path;

    global_blackboard_->set("max_docking_retries", max_docking_retries);

    if (otto_docking->type == "docking")
    {
        tree_definition_path = "/config/docking_definition_tree.xml";
    }
    else if (otto_docking->type == "virtualdocking")
    {
        tree_definition_path = "/config/virtualdocking_definition_tree.xml";

        // set target pose map frame on blackboard
        global_blackboard_->set("target_pose_map_frame", poseStampedToString(otto_docking->target_pose));
    }
    else if (otto_docking->type == "undocking")
    {
        tree_definition_path = "/config/undocking_definition_tree.xml";
    }
    else if (otto_docking->type == "charge_monitoring")
    {
        tree_definition_path = "/config/charge_monitoring_definition_tree.xml";
    }
    else
    {
        ros::Duration(1.0).sleep();
        ROS_ERROR_STREAM("Rejecting goal, invalid docking_type: " + otto_docking->type);

        ottonomy_msgs::OttoDockResult result;
        result.id = docking_id_;
        result.action_completed = false;
        dockAS->setAborted(result, "Rejecting goal, invalid docking_type: " + otto_docking->type);

        return;
    }

    // create the tree from definition file
    BT::Tree tree = bt_factory_.createTreeFromFile(otto_docking_package_path + tree_definition_path, global_blackboard_);

    // Create a transition logger
    TransitionLogger transition_logger(tree, bt_node_transitions_pub_);

    tree.tickWhileRunning();

    ROS_INFO("exiting execute_cb");
    return;
}

int main(int argc, char **argv)
{
    ros::init(argc, argv, "otto_docking_node");
    nh = new ros::NodeHandle();
    tf_listener = new tf2_ros::TransformListener(tf_buffer);

    marker_pub = nh->advertise<visualization_msgs::Marker>("docking_markers", 1);
    cmd_vel_pub = nh->advertise<geometry_msgs::Twist>("/cmd_vel", 1);
    docking_state_pub = nh->advertise<ottonomy_msgs::DockingState>("docking_state", 1);

    if (!load_params())
    {
        exit(0);
    }

    bool got_camera_tf = false;
    while (!got_camera_tf)
    {
        try
        {
            baseLinkNav_camera_tf = tf_buffer.lookupTransform("base_link_nav", camera_frame, ros::Time(0));
            got_camera_tf = true;
        }
        catch (tf2::TransformException &ex)
        {
            ROS_WARN("Unable to get tf from base_link_nav to camera frame, error %s", ex.what());
            ros::Duration(0.5).sleep();
        }
    }
    ROS_INFO("Got tf from base_link_nav to camera frame");

    // simple actions
    DockingDriveMode docking_drive_mode(*nh);
    bt_factory_.registerSimpleAction("DockingDriveModeStart", [&](BT::TreeNode &)
                                     { return docking_drive_mode.start(); });
    bt_factory_.registerSimpleAction("DockingDriveModeStop", [&](BT::TreeNode &)
                                     { return docking_drive_mode.stop(); });

    ApriltagDetection apriltag_detection(*nh);
    bt_factory_.registerSimpleAction("ApriltagDetectionStart", [&](BT::TreeNode &)
                                     { return apriltag_detection.start(); });
    bt_factory_.registerSimpleAction("ApriltagDetectionStop", [&](BT::TreeNode &)
                                     { return apriltag_detection.stop(); });

    ReduceBackSafety reduce_back_safety(*nh);
    bt_factory_.registerSimpleAction("ReduceBackSafetyStart", [&](BT::TreeNode &)
                                     { return reduce_back_safety.start(); });
    bt_factory_.registerSimpleAction("ReduceBackSafetyStop", [&](BT::TreeNode &)
                                     { return reduce_back_safety.stop(); });

    DisengageMotors disengage_motors(*nh);
    bt_factory_.registerSimpleAction("DisengageMotors", [&](BT::TreeNode &)
                                     { return disengage_motors.start(); });
    bt_factory_.registerSimpleAction("EngageMotors", [&](BT::TreeNode &)
                                     { return disengage_motors.stop(); });

    DockCommunication dock_communication(*nh);
    bt_factory_.registerSimpleAction("StartDockCommunication", [&](BT::TreeNode &)
                                     { return dock_communication.start_communication(); });
    bt_factory_.registerSimpleAction("StopDockCommunication", [&](BT::TreeNode &)
                                     { return dock_communication.stop_communication(); });
    bt_factory_.registerSimpleAction("StartDockCharging", [&](BT::TreeNode &)
                                     { return dock_communication.start_charging(); });
    bt_factory_.registerSimpleAction("StopDockCharging", [&](BT::TreeNode &)
                                     { return dock_communication.stop_charging(); });
    bt_factory_.registerSimpleAction("IsRobotCharging", [&](BT::TreeNode &)
                                     { return dock_communication.is_robot_charging(); });

    bt_factory_.registerNodeType<CheckIfMapPoseIsTooFar>("CheckIfMapPoseIsTooFar");
    bt_factory_.registerNodeType<MoveToMapPose>("MoveToMapPose");
    bt_factory_.registerNodeType<MoveToApriltagPose>("MoveToApriltagPose");
    // bt_factory_.registerNodeType<MoveToContact>("MoveToContact");

    bt_factory_.registerSimpleCondition("IsPreemptRequested", [&](BT::TreeNode &)
                                        { return IsPreemptRequested(); });
    bt_factory_.registerSimpleCondition("PremptActionGoal", [&](BT::TreeNode &)
                                        { return PremptActionGoal(); });
    bt_factory_.registerSimpleCondition("AbortActionGoal", [&](BT::TreeNode &)
                                        { return AbortActionGoal(); });
    bt_factory_.registerSimpleCondition("SucceededActionGoal", [&](BT::TreeNode &)
                                        { return SucceededActionGoal(); });

    // publisher to monitor the node transitions
    bt_node_transitions_pub_ = nh->advertise<std_msgs::String>("bt_node_transitions", 500);

    // No one "own" this blackboard
    global_blackboard_ = BT::Blackboard::create();
    // This blackboard will be owned by "MainTree". Its parent is global_blackboard
    root_blackboard_ = BT::Blackboard::create(global_blackboard_);

    // write tree model for groot
    std::string tree_xml_model = BT::writeTreeNodesModelXML(bt_factory_);
    std::string otto_docking_package_path = ros::package::getPath("otto_docking");
    std::string xml_model_file = "/config/xml_model_file.xml";
    writeStringToFile(otto_docking_package_path + xml_model_file, tree_xml_model);

    // start action server
    dockAS = new OttoDockActionServer(*nh, "otto_docking", boost::bind(&execute_cb, _1), false);
    dockAS->start();

    ros::spin();

    delete dockAS;
    delete tf_listener;
    delete nh;

    return 0;
}
