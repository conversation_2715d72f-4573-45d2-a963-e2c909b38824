/**
 * @file teb_service.cpp
 * @brief ROS service node for trajectory planning using the TEB local planner.
 *
 * This node provides a service that accepts current and goal poses along with obstacles,
 * and returns a planned trajectory as a nav_msgs::Path message.
 */

#include <ros/ros.h>
#include <teb_local_planner/teb_local_planner_ros.h>
#include <visualization_msgs/Marker.h>
#include <boost/shared_ptr.hpp>
#include <boost/make_shared.hpp>
#include <geometry_msgs/PoseStamped.h>
#include <costmap_converter/ObstacleArrayMsg.h>
#include <nav_msgs/Path.h>
#include "teb_planner_service/PlanTrajectory.h"

using namespace teb_local_planner;

/**
 * @brief Pointer to the planner interface instance.
 */
PlannerInterfacePtr planner;

/**
 * @brief Pointer to the visualization helper instance.
 */
TebVisualizationPtr visual;

/**
 * @brief Vector of obstacles in TEB format.
 */
std::vector<ObstaclePtr> obst_vector;

/**
 * @brief Container for via points used in planning.
 */
ViaPointContainer via_points;

/**
 * @brief Configuration parameters for the TEB planner.
 */
TebConfig config;

/**
 * @brief Dynamic reconfigure server pointer for runtime parameter updates.
 */
boost::shared_ptr<dynamic_reconfigure::Server<TebLocalPlannerReconfigureConfig>> dynamic_recfg;

/**
 * @brief ROS subscriber for custom obstacles (currently unused).
 */
ros::Subscriber custom_obst_sub;

/**
 * @brief ROS service server for trajectory planning requests.
 */
ros::ServiceServer teb_service;

/**
 * @brief Timer callback for periodic visualization updates.
 * @param e Timer event information.
 */
void CB_publishCycle(const ros::TimerEvent &e);

/**
 * @brief Dynamic reconfigure callback to update planner parameters at runtime.
 * @param reconfig New configuration parameters.
 * @param level Level bitmask (unused).
 */
void CB_reconfigure(TebLocalPlannerReconfigureConfig &reconfig, uint32_t level);

/**
 * @brief Service callback to plan a trajectory given current pose, goal pose, and obstacles.
 * @param req Service request containing current pose, goal pose, and obstacles.
 * @param res Service response containing the planned trajectory as nav_msgs::Path.
 * @return True if planning was successful, false otherwise.
 */
bool planTrajectory(teb_planner_service::PlanTrajectory::Request &req, teb_planner_service::PlanTrajectory::Response &res);

/**
 * @brief Main entry point for the teb_service node.
 *
 * Initializes ROS, loads parameters, sets up dynamic reconfigure, visualization,
 * robot footprint, and advertises the trajectory planning service.
 *
 * @param argc Argument count.
 * @param argv Argument vector.
 * @return Exit status code.
 */
int main(int argc, char **argv)
{
    ros::init(argc, argv, "test_teb_service");
    ros::NodeHandle n("~");

    config.loadRosParamFromNodeHandle(n);

    // Setup dynamic reconfigure
    dynamic_recfg = boost::make_shared<dynamic_reconfigure::Server<TebLocalPlannerReconfigureConfig>>(n);
    dynamic_reconfigure::Server<TebLocalPlannerReconfigureConfig>::CallbackType cb = boost::bind(CB_reconfigure, _1, _2);
    dynamic_recfg->setCallback(cb);

    // Setup visualization
    visual = TebVisualizationPtr(new TebVisualization(n, config));

    // Setup robot shape model
    config.robot_model = TebLocalPlannerROS::getRobotFootprintFromParamServer(n, config);

    // Advertise the trajectory planning service
    teb_service = n.advertiseService("plan_trajectory", planTrajectory);
    ROS_INFO("TEB Planner Service Ready!");

    // ros::Timer publish_timer = n.createTimer(ros::Duration(0.1), CB_publishCycle);

    ros::spin();
    return 0;
}

/**
 * @brief Plans a trajectory from the current pose to the goal pose avoiding obstacles.
 *
 * Converts obstacles from the request message to TEB format, selects the planner type,
 * performs the planning, and fills the response with the planned trajectory.
 *
 * @param req Service request containing current pose, goal pose, and obstacles.
 * @param res Service response to be filled with the planned trajectory.
 * @return True if planning succeeded, false otherwise.
 */
bool planTrajectory(teb_planner_service::PlanTrajectory::Request &req,
                    teb_planner_service::PlanTrajectory::Response &res)
{
    ROS_INFO("Received request for trajectory planning.");

    // Convert obstacles from message to TEB format
    obst_vector.clear();
    for (const auto &obst : req.obstacles.obstacles)
    {
        if (obst.polygon.points.size() == 1)
        {
            obst_vector.push_back(ObstaclePtr(new PointObstacle(obst.polygon.points[0].x, obst.polygon.points[0].y)));
        }
        else
        {
            PolygonObstacle *poly_obst = new PolygonObstacle;
            for (const auto &point : obst.polygon.points)
            {
                poly_obst->pushBackVertex(point.x, point.y);
            }
            poly_obst->finalizePolygon();
            obst_vector.push_back(ObstaclePtr(poly_obst));
        }
    }

    // Select planner type
    if (config.hcp.enable_homotopy_class_planning)
        planner = PlannerInterfacePtr(new HomotopyClassPlanner(config, &obst_vector, visual, &via_points));
    else
        planner = PlannerInterfacePtr(new TebOptimalPlanner(config, &obst_vector, visual, &via_points));

    // Convert current pose and goal pose to PoseSE2
    PoseSE2 start(req.current_pose.pose.position.x, req.current_pose.pose.position.y, tf::getYaw(req.current_pose.pose.orientation));
    PoseSE2 goal(req.goal_pose.pose.position.x, req.goal_pose.pose.position.y, tf::getYaw(req.goal_pose.pose.orientation));

    // Plan trajectory
    if (!planner->plan(start, goal))
    {
        ROS_WARN("TEB Planner failed to compute a trajectory!");
        return false;
    }

    // Get best trajectory
    TebOptimalPlannerPtr planner_optimal = boost::dynamic_pointer_cast<TebOptimalPlanner>(planner);
    if (!planner_optimal)
    {
        HomotopyClassPlannerPtr planner_homotopy = boost::dynamic_pointer_cast<HomotopyClassPlanner>(planner);
        if (planner_homotopy)
            planner_optimal = planner_homotopy->bestTeb();
    }

    if (!planner_optimal)
    {
        ROS_ERROR("Failed to retrieve the optimal trajectory from the planner!");
        return false;
    }

    nav_msgs::Path teb_path;
    teb_path.header.frame_id = config.map_frame;
    teb_path.header.stamp = ros::Time::now();

    for (int i = 0; i < planner_optimal->teb().sizePoses(); i++)
    {
        geometry_msgs::PoseStamped pose;
        pose.header = teb_path.header;
        pose.pose.position.x = planner_optimal->teb().Pose(i).x();
        pose.pose.position.y = planner_optimal->teb().Pose(i).y();
        pose.pose.position.z = config.hcp.visualize_with_time_as_z_axis_scale * planner_optimal->teb().getSumOfTimeDiffsUpToIdx(i);
        pose.pose.orientation = tf::createQuaternionMsgFromYaw(planner_optimal->teb().Pose(i).theta());
        teb_path.poses.push_back(pose);
    }

    res.trajectory = teb_path;
    ROS_INFO("Trajectory successfully planned and sent.");
    return true;
}

/**
 * @brief Timer callback for visualization updates.
 *
 * Publishes the current planned trajectory, obstacles, and via points for visualization.
 *
 * @param e Timer event information.
 */
void CB_publishCycle(const ros::TimerEvent &e)
{
    if (planner)
    {
        planner->visualize();
        visual->publishObstacles(obst_vector);
        visual->publishViaPoints(via_points);
    }
}

/**
 * @brief Dynamic reconfigure callback to update planner configuration.
 *
 * @param reconfig New configuration parameters.
 * @param level Level bitmask (unused).
 */
void CB_reconfigure(TebLocalPlannerReconfigureConfig &reconfig, uint32_t level)
{
    config.reconfigure(reconfig);
}
