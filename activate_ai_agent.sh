#!/bin/bash

# AI Agent Clean Environment Activation Script
# This script activates the clean virtual environment for the AI comment generation project

# Check if the script is being sourced
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "❌ This script must be sourced, not executed."
    echo "Please run it as: source activate_ai_agent.sh"
    exit 1
fi

# Check if a virtual environment is already active
if [ -n "$VIRTUAL_ENV" ]; then
    # If the active environment is the one we want, do nothing.
    if [ "$VIRTUAL_ENV" == "$(pwd)/venv_clean_isolated" ]; then
        echo "✅ AI Agent environment is already active."
    else
        echo "⚠️  Another virtual environment is active: $(basename $VIRTUAL_ENV)"
        echo "Please run 'deactivate' before activating the AI Agent environment."
    fi
    return
fi

echo "🚀 Activating AI Agent environment..."


# Check if virtual environment exists
if [ ! -d "venv_clean_isolated" ]; then
    echo "❌ Error: Virtual environment 'venv_clean_isolated' not found!"
    echo "Please run the setup first or check if you're in the correct directory."
    exit 1
fi

# Activate the virtual environment
source venv_clean_isolated/bin/activate

# Verify activation
if [ "$VIRTUAL_ENV" != "" ]; then
    echo "✅ AI Agent environment activated successfully!"
    echo "📦 Environment: $(basename $VIRTUAL_ENV)"
    echo "🐍 Python: $(python --version)"
    echo "📍 Location: $VIRTUAL_ENV"
    echo ""
    echo "🎯 Ready for AI comment generation development!"
    echo "💡 To deactivate, run: deactivate"
else
    echo "❌ Failed to activate virtual environment"
    exit 1
fi
