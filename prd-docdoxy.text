so i haave added a @Doxygen-doc so i want to better documentation from the current one i did this steps i am sharing you the terminal commands i ran https://docs.google.com/document/d/1APllG19u0FRIO289pOENq72_KgcRus6OdsFsXwlbA4s/edit?tab=t.0 THIS IS a doc for whihc i am using to create Doxygen documentation and i am saring you a the terminal steps i take so far to do this steps which are in detail steps so please look at it and make a one better documentation (venv) otto_cs_03@ottocs03:<del>/ai_agent/data/generated-output/analysis_20250513_132754$ cd docs/ (venv) otto_cs_03@ottocs03:</del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ sphinx-quickstart Welcome to the Sphinx 7.1.2 quickstart utility. Please enter values for the following settings (just press Enter to accept a default value, if one is given in brackets). Selected root path: . You have two options for placing the build directory for Sphinx output. Either, you use a directory "_build" within the root path, or you separate "source" and "build" directories within the root path. > Separate source and build directories (y/n) [n]: y The project name will occur in several places in the built documentation. > Project name: teb-palnner-testing > Author name(s): ottonomy > Project release []: V.0.0 If the documents are to be written in a language other than English, you can select a language here by its language code. Sphinx will then translate text that it generates into that language. For a list of supported codes, see https://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language. > Project language [en]: en Creating file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/conf.py. Creating file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/index.rst. Creating file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/Makefile. Creating file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/make.bat. Finished: An initial directory structure has been created. You should now populate your master file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/index.rst and create other documentation source files. Use the Makefile to build the docs, like so: make builder where "builder" is one of the supported builders, e.g. html, latex or linkcheck. (venv) otto_cs_03@ottocs03:<del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ doxygen -g Doxyfile Configuration file 'Doxyfile' created. Now edit the configuration file and enter doxygen Doxyfile to generate the documentation for your project (venv) otto_cs_03@ottocs03:</del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ mkdir -p build/doxygen (venv) otto_cs_03@ottocs03:<del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ doxygen Doxyfile Searching for include files... Searching for example files... Searching for images... Searching for dot files... Searching for msc files... Searching for dia files... Searching for files to exclude Searching INPUT for files to process... Searching for files in directory /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src Reading and parsing tag files Parsing files Preprocessing /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp... Parsing file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp... Preprocessing /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp... Parsing file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp... Preprocessing /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/teb_service.cpp... Parsing file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/teb_service.cpp... Reading /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/package.xml... Building group list... Building directory list... Building namespace list... Building file list... Building class list... Computing nesting relations for classes... Associating documentation with classes... Building example list... Searching for enumerations... Searching for documented typedefs... Searching for members imported via using declarations... Searching for included using directives... Searching for documented variables... Building interface member list... Building member list... Searching for friends... Searching for documented defines... Computing class inheritance relations... Computing class usage relations... Flushing cached template relations that have become invalid... Computing class relations... Add enum values to enums... Searching for member function documentation... Creating members for template instances... Building page list... Search for main page... Computing page relations... Determining the scope of groups... Sorting lists... Determining which enums are documented Computing member relations... Building full member lists recursively... Adding members to member groups. Computing member references... Inheriting documentation... Generating disk names... Adding source references... Adding xrefitems... Sorting member lists... Setting anonymous enum type... Computing dependencies between directories... Generating citations page... Counting members... Counting data structures... Resolving user defined references... Finding anchors and sections in the documentation... Transferring function references... Combining using relations... Adding members to index pages... Correcting members for VHDL... Generating style sheet... Generating search indices... Generating example documentation... Generating file sources... Generating file documentation... Generating docs for file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/package.xml... Generating docs for file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp... /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:254: warning: argument 'e' from the argument list of CB_publishCycle has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:270: warning: argument 'reconfig' from the argument list of CB_reconfigure has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:270: warning: argument 'level' from the argument list of CB_reconfigure has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:152: warning: The following parameter of planTrajectory(teb_planner_service::PlanTrajectory::Request &req, teb_planner_service::PlanTrajectory::Response &res) is not documented: parameter 'req' Generating docs for file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp... /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp:253: warning: argument 'e' from the argument list of CB_publishCycle has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp:269: warning: argument 'reconfig' from the argument list of CB_reconfigure has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp:269: warning: argument 'level' from the argument list of CB_reconfigure has multiple @param documentation sections Generating docs for file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/teb_service.cpp... /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/teb_service.cpp:83: warning: The following parameter of planTrajectory(teb_planner_service::PlanTrajectory::Request &req, teb_planner_service::PlanTrajectory::Response &res) is not documented: parameter 'req' Generating page documentation... Generating group documentation... Generating class documentation... Generating namespace index... Generating graph info page... Generating directory documentation... Generating index page... Generating page index... Generating module index... Generating namespace index... Generating namespace member index... Generating annotated compound index... Generating alphabetical compound index... Generating hierarchical class index... Generating graphical class hierarchy... Generating member index... Generating file index... Generating file member index... Generating example index... finalizing index lists... writing tag file... Generating XML output... Generating XML output for namespace teb_local_planner Generating XML output for file package.xml Generating XML output for file new_1.cpp /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:254: warning: argument 'e' from the argument list of CB_publishCycle has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:270: warning: argument 'reconfig' from the argument list of CB_reconfigure has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:270: warning: argument 'level' from the argument list of CB_reconfigure has multiple @param documentation sections Generating XML output for file new_2.cpp /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp:253: warning: argument 'e' from the argument list of CB_publishCycle has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp:269: warning: argument 'reconfig' from the argument list of CB_reconfigure has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp:269: warning: argument 'level' from the argument list of CB_reconfigure has multiple @param documentation sections Generating XML output for file teb_service.cpp Generate XML output for dir /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/ Running plantuml with JAVA... Running dot... Generating dot graphs using 13 parallel threads... Running dot for graph 1/7 Running dot for graph 2/7 Running dot for graph 3/7 Running dot for graph 4/7 Running dot for graph 5/7 Running dot for graph 6/7 Running dot for graph 7/7 Patching output file 1/6 Patching output file 2/6 Patching output file 3/6 Patching output file 4/6 Patching output file 5/6 Patching output file 6/6 lookup cache used 65/65536 hits=438 misses=65 finished... (venv) otto_cs_03@ottocs03:</del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ (venv) otto_cs_03@ottocs03:<del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ touch docs/source/overview.rst\ > ^C (venv) otto_cs_03@ottocs03:</del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ touch docs/source/overview.rst touch: cannot touch 'docs/source/overview.rst': No such file or directory (venv) otto_cs_03@ottocs03:<del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ touch /source/overview.rst touch: cannot touch '/source/overview.rst': No such file or directory (venv) otto_cs_03@ottocs03:</del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ touch source/overview.rst (venv) otto_cs_03@ottocs03:<del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ tou^C (venv) otto_cs_03@ottocs03:</del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ ^C (venv) otto_cs_03@ottocs03:<del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ touch source/tutorials/index.rst touch: cannot touch 'source/tutorials/index.rst': No such file or directory (venv) otto_cs_03@ottocs03:</del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ mkdir source/tutorials (venv) otto_cs_03@ottocs03:<del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ touch source/tutorials/index.rst (venv) otto_cs_03@ottocs03:</del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ touch source/tutorials/usage.rst (venv) otto_cs_03@ottocs03:<del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ mkdir source/api/ (venv) otto_cs_03@ottocs03:</del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ touch source/api/index.rst (venv) otto_cs_03@ottocs03:<del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ doxygen Doxyfile Searching for include files... Searching for example files... Searching for images... Searching for dot files... Searching for msc files... Searching for dia files... Searching for files to exclude Searching INPUT for files to process... Searching for files in directory /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src Reading and parsing tag files Parsing files Preprocessing /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp... Parsing file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp... Preprocessing /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp... Parsing file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp... Preprocessing /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/teb_service.cpp... Parsing file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/teb_service.cpp... Reading /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/package.xml... Building group list... Building directory list... Building namespace list... Building file list... Building class list... Computing nesting relations for classes... Associating documentation with classes... Building example list... Searching for enumerations... Searching for documented typedefs... Searching for members imported via using declarations... Searching for included using directives... Searching for documented variables... Building interface member list... Building member list... Searching for friends... Searching for documented defines... Computing class inheritance relations... Computing class usage relations... Flushing cached template relations that have become invalid... Computing class relations... Add enum values to enums... Searching for member function documentation... Creating members for template instances... Building page list... Search for main page... Computing page relations... Determining the scope of groups... Sorting lists... Determining which enums are documented Computing member relations... Building full member lists recursively... Adding members to member groups. Computing member references... Inheriting documentation... Generating disk names... Adding source references... Adding xrefitems... Sorting member lists... Setting anonymous enum type... Computing dependencies between directories... Generating citations page... Counting members... Counting data structures... Resolving user defined references... Finding anchors and sections in the documentation... Transferring function references... Combining using relations... Adding members to index pages... Correcting members for VHDL... Generating style sheet... Generating search indices... Generating example documentation... Generating file sources... Generating file documentation... Generating docs for file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/package.xml... Generating docs for file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp... /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:254: warning: argument 'e' from the argument list of CB_publishCycle has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:270: warning: argument 'reconfig' from the argument list of CB_reconfigure has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:270: warning: argument 'level' from the argument list of CB_reconfigure has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:152: warning: The following parameter of planTrajectory(teb_planner_service::PlanTrajectory::Request &req, teb_planner_service::PlanTrajectory::Response &res) is not documented: parameter 'req' Generating docs for file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp... /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp:253: warning: argument 'e' from the argument list of CB_publishCycle has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp:269: warning: argument 'reconfig' from the argument list of CB_reconfigure has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp:269: warning: argument 'level' from the argument list of CB_reconfigure has multiple @param documentation sections Generating docs for file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/teb_service.cpp... /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/teb_service.cpp:83: warning: The following parameter of planTrajectory(teb_planner_service::PlanTrajectory::Request &req, teb_planner_service::PlanTrajectory::Response &res) is not documented: parameter 'req' Generating page documentation... Generating group documentation... Generating class documentation... Generating namespace index... Generating graph info page... Generating directory documentation... Generating index page... Generating page index... Generating module index... Generating namespace index... Generating namespace member index... Generating annotated compound index... Generating alphabetical compound index... Generating hierarchical class index... Generating graphical class hierarchy... Generating member index... Generating file index... Generating file member index... Generating example index... finalizing index lists... writing tag file... Generating XML output... Generating XML output for namespace teb_local_planner Generating XML output for file package.xml Generating XML output for file new_1.cpp /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:254: warning: argument 'e' from the argument list of CB_publishCycle has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:270: warning: argument 'reconfig' from the argument list of CB_reconfigure has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:270: warning: argument 'level' from the argument list of CB_reconfigure has multiple @param documentation sections Generating XML output for file new_2.cpp /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp:253: warning: argument 'e' from the argument list of CB_publishCycle has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp:269: warning: argument 'reconfig' from the argument list of CB_reconfigure has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp:269: warning: argument 'level' from the argument list of CB_reconfigure has multiple @param documentation sections Generating XML output for file teb_service.cpp Generate XML output for dir /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/ Running plantuml with JAVA... Running dot... lookup cache used 65/65536 hits=438 misses=65 finished... (venv) otto_cs_03@ottocs03:</del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ doxygen Doxyfile^C (venv) otto_cs_03@ottocs03:<del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ cd ../ (venv) otto_cs_03@ottocs03:</del>/ai_agent/data/generated-output/analysis_20250513_132754$ rosdoc_lite . -o docs/source/ros Documenting a catkin package Documenting analysis_20250513_132754 located here: /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754 {'doxygen': {'builder': 'doxygen', 'output_dir': '.'}} {'builder': 'doxygen', 'output_dir': '.'} Generated the following tagfile string doxygen-ating analysis_20250513_132754 [doxygen /tmp/tmpxh7cn4tv] warning: Tag 'PERL_PATH' at line 1996 of file '/tmp/tmpxh7cn4tv' has become obsolete. To avoid this warning please remove this line from your configuration file or upgrade it using "doxygen -u" warning: Tag 'MSCGEN_PATH' at line 2018 of file '/tmp/tmpxh7cn4tv' has become obsolete. To avoid this warning please remove this line from your configuration file or upgrade it using "doxygen -u" /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:254: warning: argument 'e' from the argument list of CB_publishCycle has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:270: warning: argument 'reconfig' from the argument list of CB_reconfigure has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:270: warning: argument 'level' from the argument list of CB_reconfigure has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_1.cpp:152: warning: The following parameter of planTrajectory(teb_planner_service::PlanTrajectory::Request &req, teb_planner_service::PlanTrajectory::Response &res) is not documented: parameter 'req' /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp:253: warning: argument 'e' from the argument list of CB_publishCycle has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp:269: warning: argument 'reconfig' from the argument list of CB_reconfigure has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/new_2.cpp:269: warning: argument 'level' from the argument list of CB_reconfigure has multiple @param documentation sections /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/teb_service.cpp:83: warning: The following parameter of planTrajectory(teb_planner_service::PlanTrajectory::Request &req, teb_planner_service::PlanTrajectory::Response &res) is not documented: parameter 'req' [] [] [] copying /opt/ros/noetic/lib/python3/dist-packages/rosdoc_lite/templates/msg-styles.css to docs/source/ros/html/msg-styles.css Done documenting analysis_20250513_132754 you can find your documentation here: /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/ros (venv) otto_cs_03@ottocs03:<del>/ai_agent/data/generated-output/analysis_20250513_132754$ cd docs/ build/ html/ latex/ source/ (venv) otto_cs_03@ottocs03:</del>/ai_agent/data/generated-output/analysis_20250513_132754$ cd docs/source/ros/ (venv) otto_cs_03@ottocs03:<del>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/ros$ touch index.rst (venv) otto_cs_03@ottocs03:</del>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/ros$ touch teb_planner_node.rst (venv) otto_cs_03@ottocs03:<del>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/ros$ cd ../../ 4(venv) otto_cs_03@ottocs03:</del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ 4^C (venv) otto_cs_03@ottocs03:<del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ make html Running Sphinx v7.1.2 making output directory... done building [mo]: targets for 0 po files that are out of date writing output... building [html]: targets for 7 source files that are out of date updating environment: [new config] 7 added, 0 changed, 0 removed reading sources... [100%] tutorials/usage /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Invalid C++ declaration: Expected identifier in nested name. [error at 0] *brief Dynamic reconfigure server for TEB planner parameters *boost::shared_ptr< dynamic_reconfigure::Server< TebLocalPlannerReconfigureConfig > > dynamic_recfg ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Invalid C++ declaration: Expected identifier in nested name. [error at 0] *ros::ServiceServer teb_service ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Invalid C++ declaration: Expected identifier in nested name. [error at 0] **param req Service request containing current pose ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Invalid C++ declaration: Expected identifier in nested name. [error at 0] **param req Service request containing current goal and obstacles *param res Service response containing the planned trajectory as nav_msgs Path *return true if planning was successful ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Error when parsing function declaration. If the function has no return type: Error in declarator or parameters-and-qualifiers Invalid C++ declaration: Expected identifier in nested name. [error at 0] *brief Global pointer to the planner interface (TEB or Homotopy). */PlannerInterfacePtr planner ^ If the function has a return type: Invalid C++ declaration: Expected identifier in nested name. [error at 0] *brief Global pointer to the planner interface (TEB or Homotopy). */PlannerInterfacePtr planner ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Duplicate C++ declaration, also defined at api/index:4. Declaration is '.. cpp:member:: TebVisualizationPtr visual'. /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Invalid C++ declaration: Expected identifier in nested name. [error at 0] *std::vector< ObstaclePtr > obst_vector ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Duplicate C++ declaration, also defined at api/index:4. Declaration is '.. cpp:member:: ViaPointContainer via_points'. /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Invalid C++ declaration: Expected identifier in nested name. [error at 0] *brief Configuration parameters for the TEB planner *TebConfig config ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Duplicate C++ declaration, also defined at api/index:4. Declaration is '.. cpp:member:: ros::Subscriber custom_obst_sub'. /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Invalid C++ declaration: Expected identifier in nested name. [error at 0] *Initializes ROS ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Invalid C++ declaration: Expected identifier in nested name. [error at 0] *Initializes loads parameters ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Invalid C++ declaration: Expected identifier in nested name. [error at 0] *Initializes loads sets up dynamic reconfigure ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Invalid C++ declaration: Expected identifier in nested name. [error at 0] *Initializes loads sets up dynamic visualization ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Invalid C++ declaration: Expected identifier in nested name. [error at 0] *Initializes loads sets up dynamic *robot footprint ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Invalid C++ declaration: Expected identifier in nested name. [error at 0] *brief Plans a trajectory from the current pose to the goal pose avoiding obstacles **Converts obstacles from the request message to TEB format ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Invalid C++ declaration: Expected identifier in nested name. [error at 0] *brief Plans a trajectory from the current pose to the goal pose avoiding obstacles **Converts obstacles from the request message to TEB selects the planner type ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Invalid C++ declaration: Expected identifier in nested name. [error at 0] *brief Plans a trajectory from the current pose to the goal pose avoiding obstacles **Converts obstacles from the request message to TEB selects the planner *performs the planning ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Invalid C++ declaration: Expected identifier in nested name. [error at 0] *brief Plans a trajectory from the current pose to the goal pose avoiding obstacles **Converts obstacles from the request message to TEB selects the planner *performs the and fills the response with the planned trajectory **param req Service request containing current pose ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Invalid C++ declaration: Expected identifier in nested name. [error at 0] *brief Plans a trajectory from the current pose to the goal pose avoiding obstacles **Converts obstacles from the request message to TEB selects the planner *performs the and fills the response with the planned trajectory **param req Service request containing current goal and obstacles *param res Service response to be filled with the planned trajectory *return True if planning succeeded ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Duplicate C++ declaration, also defined at api/index:4. Declaration is '.. cpp:function:: void CB_publishCycle (const ros::TimerEvent &e)'. /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Error when parsing function declaration. If the function has no return type: Error in declarator or parameters-and-qualifiers Invalid C++ declaration: Expected identifier in nested name. [error at 0] *void CB_reconfigure (TebLocalPlannerReconfigureConfig &reconfig, uint32_t level) ^ If the function has a return type: Invalid C++ declaration: Expected identifier in nested name. [error at 0] *void CB_reconfigure (TebLocalPlannerReconfigureConfig &reconfig, uint32_t level) ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Duplicate C++ declaration, also defined at api/index:4. Declaration is '.. cpp:function:: bool planTrajectory (teb_planner_service::PlanTrajectory::Request &req, teb_planner_service::PlanTrajectory::Response &res)'. /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Error when parsing function declaration. If the function has no return type: Error in declarator or parameters-and-qualifiers Invalid C++ declaration: Expected identifier in nested name. [error at 0] *Initializes loads sets up dynamic *robot and advertises the trajectory planning service **param argc Argument count *param argv Argument vector *return Exit status code *int main (int argc, char **argv) ^ If the function has a return type: Invalid C++ declaration: Expected identifier in nested name. [error at 0] *Initializes loads sets up dynamic *robot and advertises the trajectory planning service **param argc Argument count *param argv Argument vector *return Exit status code *int main (int argc, char **argv) ^ /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Duplicate C++ declaration, also defined at api/index:4. Declaration is '.. cpp:member:: PlannerInterfacePtr planner'. /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Duplicate C++ declaration, also defined at api/index:4. Declaration is '.. cpp:member:: TebVisualizationPtr visual'. /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Duplicate C++ declaration, also defined at api/index:4. Declaration is '.. cpp:member:: std::vector< ObstaclePtr > obst_vector'. /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Duplicate C++ declaration, also defined at api/index:4. Declaration is '.. cpp:member:: ViaPointContainer via_points'. /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Duplicate C++ declaration, also defined at api/index:4. Declaration is '.. cpp:member:: TebConfig config'. /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Duplicate C++ declaration, also defined at api/index:4. Declaration is '.. cpp:member:: boost::shared_ptr< dynamic_reconfigure::Server< TebLocalPlannerReconfigureConfig > > dynamic_recfg'. /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Duplicate C++ declaration, also defined at api/index:4. Declaration is '.. cpp:member:: ros::Subscriber custom_obst_sub'. /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Duplicate C++ declaration, also defined at api/index:4. Declaration is '.. cpp:member:: ros::ServiceServer teb_service'. /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Duplicate C++ declaration, also defined at api/index:4. Declaration is '.. cpp:function:: void CB_publishCycle (const ros::TimerEvent &e)'. /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Duplicate C++ declaration, also defined at api/index:4. Declaration is '.. cpp:function:: void CB_reconfigure (TebLocalPlannerReconfigureConfig &reconfig, uint32_t level)'. /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Duplicate C++ declaration, also defined at api/index:4. Declaration is '.. cpp:function:: bool planTrajectory (teb_planner_service::PlanTrajectory::Request &req, teb_planner_service::PlanTrajectory::Response &res)'. /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/api/index.rst:4: WARNING: Duplicate C++ declaration, also defined at api/index:4. Declaration is '.. cpp:function:: int main (int argc, char **argv)'. /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/index.rst:4: WARNING: Title underline too short. teb_planner_service ================== /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/overview.rst:11: WARNING: image file not readable: _static/Screenshotfrom2025-03-3119-11-16.png /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/tutorials/usage.rst:2: WARNING: Title underline too short. Using the Package ================ looking for now-outdated files... none found pickling environment... done checking consistency... done preparing documents... done copying assets... copying static files... done copying extra files... done done writing output... [100%] tutorials/usage generating indices... genindex done writing additional pages... search done dumping search index in English (code: en)... done dumping object inventory... done build succeeded, 39 warnings. The HTML pages are in build/html. (venv) otto_cs_03@ottocs03:</del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ make html^C (venv) otto_cs_03@ottocs03:<del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ ^C (venv) otto_cs_03@ottocs03:</del>/ai_agent/data/generated-output/analysis_20250513_132754/docs$ doxygen Doxyfile Searching for include files... Searching for example files... Searching for images... Searching for dot files... Searching for msc files... Searching for dia files... Searching for files to exclude Searching INPUT for files to process... Searching for files in directory /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src Reading and parsing tag files Parsing files Preprocessing /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/teb_service.cpp... Parsing file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/teb_service.cpp... Building group list... Building directory list... Building namespace list... Building file list... Building class list... Computing nesting relations for classes... Associating documentation with classes... Building example list... Searching for enumerations... Searching for documented typedefs... Searching for members imported via using declarations... Searching for included using directives... Searching for documented variables... Building interface member list... Building member list... Searching for friends... Searching for documented defines... Computing class inheritance relations... Computing class usage relations... Flushing cached template relations that have become invalid... Computing class relations... Add enum values to enums... Searching for member function documentation... Creating members for template instances... Building page list... Search for main page... Computing page relations... Determining the scope of groups... Sorting lists... Determining which enums are documented Computing member relations... Building full member lists recursively... Adding members to member groups. Computing member references... Inheriting documentation... Generating disk names... Adding source references... Adding xrefitems... Sorting member lists... Setting anonymous enum type... Computing dependencies between directories... Generating citations page... Counting members... Counting data structures... Resolving user defined references... Finding anchors and sections in the documentation... Transferring function references... Combining using relations... Adding members to index pages... Correcting members for VHDL... Generating style sheet... Generating search indices... Generating example documentation... Generating file sources... Generating file documentation... Generating docs for file /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/teb_service.cpp... Generating page documentation... Generating group documentation... Generating class documentation... Generating namespace index... Generating graph info page... Generating directory documentation... Generating index page... Generating page index... Generating module index... Generating namespace index... Generating namespace member index... Generating annotated compound index... Generating alphabetical compound index... Generating hierarchical class index... Generating graphical class hierarchy... Generating member index... Generating file index... Generating file member index... Generating example index... finalizing index lists... writing tag file... Generating XML output... Generating XML output for namespace teb_local_planner Generating XML output for file teb_service.cpp Generate XML output for dir /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/src/ Running plantuml with JAVA... Running dot... Generating dot graphs using 4 parallel threads... Running dot for graph 1/2 Running dot for graph 2/2 Patching output file 1/2 Patching output file 2/2 lookup cache used 13/65536 hits=49 misses=13 finished... (venv) otto_cs_03@ottocs03:~/ai_agent/data/generated-output/analysis_20250513_132754/docs$ make html Running Sphinx v7.1.2 loading pickled environment... done building [mo]: targets for 0 po files that are out of date writing output... building [html]: targets for 0 source files that are out of date updating environment: 0 added, 2 changed, 0 removed reading sources... [100%] overview /home/<USER>/ai_agent/data/generated-output/analysis_20250513_132754/docs/source/overview.rst:11: WARNING: image file not readable: _static/Screenshotfrom2025-03-3119-11-16.png looking for now-outdated files... none found pickling environment... done checking consistency... done preparing documents... done copying assets... copying static files... done copying extra files... done done writing output... [100%] overview generating indices... genindex done writing additional pages... search done dumping search index in English (code: en)... done dumping object inventory... done build succeeded, 1 warning. The HTML pages are in build/html. https://docs.google.com/document/d/1APllG19u0FRIO289pOENq72_KgcRus6OdsFsXwlbA4s/edit?tab=t.0 this is a exteranl weblink so i want your help to make a detailed documentation fo the steps i took and the the steps mentioned in the documentation so then we can use the new documenttation to make the process automate

