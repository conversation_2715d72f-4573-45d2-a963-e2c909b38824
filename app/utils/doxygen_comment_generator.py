import os
import json
import logging
from typing import List, Dict, Any, Optional
from app.llm.base import BaseDocGenerator
from app.utils.code_analyzer import CodeAnalyzer
from app.utils.language_detector import detect_language

logger = logging.getLogger(__name__)

class DoxygenCommentGenerator(BaseDocGenerator):
    """
    Comment generator that produces Doxygen-compatible comments.
    Extends the base document generator to format comments in Doxygen style.
    """
    
    def __init__(self, llm_generator: BaseDocGenerator):
        """
        Initialize with an LLM-based generator for the actual comment generation.
        
        @param llm_generator An instance of a BaseDocGenerator that uses an LLM
        """
        self.llm_generator = llm_generator
        self.code_analyzer = CodeAnalyzer()
        
    async def generate_file_documentation(self, file_content: str, file_path: str) -> Dict[str, str]:
        """
        Generate documentation for a single file with Doxygen-compatible comments.
        
        @param file_content The content of the file
        @param file_path Path to the file
        @return Dictionary with 'inline_comments' and 'file_summary'
        """
        # First, analyze the code structure
        temp_file_path = self._save_temp_file(file_content)
        file_structure = self.code_analyzer.get_file_structure(temp_file_path)
        os.remove(temp_file_path)  # Clean up temp file
        
        # Generate comments with enhanced context
        doxygen_comments = await self.generate_doxygen_comments(file_content, file_path)
        
        # Generate file summary using the LLM generator
        file_docs = await self.llm_generator.generate_file_documentation(file_content, file_path)
        file_summary = file_docs['file_summary']
        
        return {
            "inline_comments": doxygen_comments,
            "file_summary": file_summary
        }
    
    async def generate_doxygen_comments(self, file_content: str, file_path: str) -> list:
        """
        Generate Doxygen-compatible comments for a given file.

        Args:
            file_content: The content of the file
            file_path: The path to the file

        Returns:
            A list of Doxygen-compatible comments
        """
        try:
            language = detect_language(file_path)
            file_structure = self.code_analyzer.get_file_structure(file_path)

            context = {
                "file_path": file_path,
                "language": language,
                "functions": file_structure.get('functions', []),
                "classes": file_structure.get('classes', []),
                "imports": file_structure.get('imports', []),
                "variables": file_structure.get('variables', []),
                "complexity_points": file_structure.get('complexity_points', [])
            }

            context_json = json.dumps(context, indent=2)
            doxygen_prompt = f"""
            You are an expert in generating Doxygen-compatible comments for {language} code.
            Use the following code structure context to generate precise comments:
            {context_json}

            Code:
            {file_content}

            Generate Doxygen comments for:
            - File header: @file, @brief, <AUTHOR> Functions: @brief, @param, @return
            - Classes: @brief, @details
            - Variables: @brief
            - Complex logic: @note for non-obvious implementations

            Format as a JSON array:
            [
                {{"line_number": 1, "comment": "/**\\n * @file {file_path}\\n * @brief File description\\n * <AUTHOR> by AI\\n */"}},
                {{"line_number": 10, "comment": "/**\\n * @brief Function description\\n * @param param Description\\n * @return Return description\\n */"}}
            ]

            Rules:
            - Be concise and technical
            - Focus on 'why' for complex logic
            - Skip obvious code
            - Ensure valid Doxygen syntax
            """

            raw_comments = await self.llm_generator.generate_inline_comments(doxygen_prompt, language)
            return raw_comments if raw_comments else []

        except Exception as e:
            logger.error(f"Failed to generate Doxygen comments: {e}")
            return []
    
    def _save_temp_file(self, content: str) -> str:
        """
        Save content to a temporary file for analysis.
        
        @param content File content to save
        @return Path to the temporary file
        """
        import tempfile
        
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.py')
        temp_file.write(content.encode('utf-8'))
        temp_file.close()
        
        return temp_file.name
        
    async def generate_repo_summary(self, file_structure: List[Dict], key_files_content: Dict) -> str:
        """
        Generate high-level repository documentation.
        
        @param file_structure List of file information dictionaries
        @param key_files_content Dictionary mapping file paths to their content
        @return Repository summary as a string
        """
        # Delegate to the LLM generator
        return await self.llm_generator.generate_repo_summary(file_structure, key_files_content)
        
    async def generate_inline_comments(self, code: str, language: str, file_path: str = "") -> List[Dict[str, Any]]:
        """
        Generate inline comments for code blocks.
        
        @param code The code content
        @param language The programming language
        @return List of comment dictionaries with line numbers and comment text
        """
        # This is used directly by the LLM generator in generate_doxygen_comments
        # For direct calls, delegate to the LLM generator
        return await self.llm_generator.generate_inline_comments(code, language, file_path)
