import os
import yaml
import json
import openai
import logging
import asyncio
import time
from typing import List, Dict, Any
from dotenv import load_dotenv
from app.llm.base import BaseDocGenerator
from app.llm.doxygen_prompts import DoxygenPromptGenerator

load_dotenv()
logger = logging.getLogger(__name__)

class OpenAICompatibleGenerator(BaseDocGenerator):
    """
    Documentation generator for OpenAI-compatible APIs (OpenAI, Groq, OpenRouter).
    Includes rate limiting and retry logic.
    """
    def __init__(self, provider: str = None):
        """Initialize the client for the specified provider."""
        self.last_request_time = 0
        self.min_request_interval = 1.0  # Minimum 1 second between requests
        self.max_retries = 3
        try:
            with open('config/config.yaml', 'r') as f:
                config = yaml.safe_load(f)

            # If provider is not specified, determine from environment
            if provider is None:
                if os.getenv("GROQ_API_KEY"):
                    provider = "groq"
                elif os.getenv("OPENAI_API_KEY"):
                    provider = "openai"
                elif os.getenv("OPENROUTER_API_KEY"):
                    provider = "openrouter"
                else:
                    raise ValueError("No API keys found for any supported OpenAI-compatible provider")

            # Provider-specific configuration
            providers = {
                "openai": {
                    "api_key": os.getenv("OPENAI_API_KEY"),
                    "base_url": "https://api.openai.com/v1",
                    "model": config.get('llm', {}).get('models', {}).get('openai', {}).get('model_name', "gpt-4")
                },
                "groq": {
                    "api_key": os.getenv("GROQ_API_KEY"),
                    "base_url": "https://api.groq.com/openai/v1",
                    "model": config.get('llm', {}).get('models', {}).get('groq', {}).get('model_name', "deepseek-r1-distill-llama-70b")
                },
                "openrouter": {
                    "api_key": os.getenv("OPENROUTER_API_KEY"),
                    "base_url": "https://openrouter.ai/api/v1",
                    "model": config.get('llm', {}).get('models', {}).get('openrouter', {}).get('model_name', "meta-ai/llama-3.1-8b-instruct")
                }
            }

            if provider not in providers:
                raise ValueError(f"Unsupported provider: {provider}")

            cfg = providers[provider]
            if not cfg["api_key"]:
                raise ValueError(f"No API key found for {provider}")

            self.client = openai.AsyncOpenAI(api_key=cfg["api_key"], base_url=cfg["base_url"])
            self.model = cfg["model"]
            logger.info(f"Initialized {provider} client with model: {self.model}")

        except Exception as e:
            logger.error(f"Failed to initialize {provider} client: {e}")
            raise

    async def _rate_limit(self):
        """Implement rate limiting between API calls."""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time

        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            logger.info(f"Rate limiting: waiting {sleep_time:.2f} seconds")
            await asyncio.sleep(sleep_time)

        self.last_request_time = time.time()

    async def generate_inline_comments(self, code: str, language: str, file_path: str = "") -> List[Dict[str, Any]]:
        """
        Generate Doxygen-compatible comments for code blocks with rate limiting and retry logic.
        """
        await self._rate_limit()

        # Log input details
        code_lines = len(code.split('\n'))
        code_chars = len(code)
        logger.info(f"🚀 {self.provider.upper()} REQUEST - Language: {language}, Lines: {code_lines}, Characters: {code_chars}")
        logger.debug(f"📝 {self.provider.upper()} INPUT CODE:\n{'-'*50}\n{code}\n{'-'*50}")

        # Generate Doxygen-compatible prompt
        prompt = DoxygenPromptGenerator.generate_doxygen_prompt(code, language, file_path)
        """

        # Log the full prompt being sent
        prompt_chars = len(prompt)
        logger.info(f"📤 GROQ/OpenAI PROMPT - Characters: {prompt_chars}")
        logger.debug(f"📤 GROQ/OpenAI FULL PROMPT:\n{'-'*50}\n{prompt}\n{'-'*50}")

        for attempt in range(self.max_retries):
            try:
                response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": "You are a code documentation expert. Provide clear, concise inline comments in valid JSON format only."},
                        {"role": "user", "content": prompt}
                    ],
                    response_format={"type": "json_object"}
                )

                content = response.choices[0].message.content

                # Log the raw response
                response_chars = len(content) if content else 0
                logger.info(f"📥 GROQ/OpenAI RESPONSE - Characters: {response_chars}")
                logger.debug(f"📥 GROQ/OpenAI RAW RESPONSE:\n{'-'*50}\n{content}\n{'-'*50}")

                # Try to parse the JSON response
                try:
                    # If the response is a JSON object with a comments array
                    parsed = json.loads(content)
                    if isinstance(parsed, dict) and "comments" in parsed:
                        comments_list = parsed["comments"]

                        # Validate and format Doxygen comments
                        valid_comments = []
                        for item in comments_list:
                            if (isinstance(item, dict) and
                                    "line_number" in item and isinstance(item["line_number"], int) and
                                    "comment" in item and isinstance(item["comment"], str)):

                                # Validate and format Doxygen comment
                                comment_text = item["comment"]

                                # Ensure proper Doxygen formatting
                                if not DoxygenPromptGenerator.validate_doxygen_comment(comment_text, language):
                                    logger.warning(f"Comment at line {item['line_number']} doesn't follow Doxygen format, formatting...")
                                    comment_text = DoxygenPromptGenerator.format_comment_for_language(comment_text, language)

                                # Add formatted comment to results
                                formatted_item = {
                                    "line_number": item["line_number"],
                                    "comment": comment_text,
                                    "type": "doxygen"  # Mark as Doxygen comment
                                }
                                valid_comments.append(formatted_item)

                        logger.info(f"✅ {self.provider.upper()} SUCCESS - Generated {len(valid_comments)} Doxygen-compatible comments")
                        logger.debug(f"✅ {self.provider.upper()} PARSED DOXYGEN COMMENTS:\n{json.dumps(valid_comments, indent=2)}")
                        return valid_comments
                    # If the response is a direct array
                    elif isinstance(parsed, list):
                        logger.warning(f"⚠️ {self.provider.upper()} returned direct array format, processing as legacy comments")
                        return parsed
                    else:
                        # Create a default structure if the JSON doesn't match expected format
                        logger.warning(f"⚠️ {self.provider.upper()} UNEXPECTED JSON: {parsed}")
                        return []
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON response: {e}")
                    logger.debug(f"Raw response: {content}")
                    if attempt == self.max_retries - 1:
                        return []
                    continue  # Try again

            except Exception as e:
                logger.warning(f"Attempt {attempt + 1}/{self.max_retries} failed: {e}")
                if attempt == self.max_retries - 1:
                    logger.error(f"All {self.max_retries} attempts failed for comment generation")
                    return []

                # Exponential backoff for retries
                wait_time = 2 ** attempt
                logger.info(f"Retrying in {wait_time} seconds...")
                await asyncio.sleep(wait_time)

        return []