import os
import yaml
import json
import logging
import asyncio
import time
import aiohttp
from typing import List, Dict, Any
from dotenv import load_dotenv
from app.llm.base import BaseDocGenerator

load_dotenv()
logger = logging.getLogger(__name__)

class GeminiDocGenerator(BaseDocGenerator):
    """
    Documentation generator using Google's Gemini API via HTTP requests.
    Focuses on inline comments.
    """
    def __init__(self):
        """Initialize the Gemini client."""
        try:
            with open('config/config.yaml', 'r') as f:
                config_data = yaml.safe_load(f)

            self.api_key = os.getenv("GEMINI_API_KEY")

            # Use gemini-1.5-flash for the REST API (correct model name for v1beta API)
            self.model_name = config_data.get('llm', {}).get('models', {}).get('gemini', {}).get('model_name', "gemini-1.5-flash")

            # Rate limiting
            self.last_request_time = 0
            self.min_request_interval = 2.0  # 2 seconds between requests for Gemini
            self.max_retries = 3

            if not self.api_key:
                raise ValueError("GEMINI_API_KEY not found in environment variables")

            # Gemini REST API endpoint
            self.api_url = f"https://generativelanguage.googleapis.com/v1beta/models/{self.model_name}:generateContent"

            logger.info(f"Initialized Gemini with API key. Using model: {self.model_name}")

        except Exception as e:
            logger.error(f"Failed to initialize Gemini client: {e}", exc_info=True)
            raise

    async def _rate_limit(self):
        """Implement rate limiting between API calls."""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time

        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            logger.info(f"Gemini rate limiting: waiting {sleep_time:.2f} seconds")
            await asyncio.sleep(sleep_time)

        self.last_request_time = time.time()

    async def generate_inline_comments(self, code: str, language: str) -> List[Dict[str, Any]]:
        """
        Generate inline comments for code blocks using google-generativeai==0.1.0rc1.
        Includes rate limiting and retry logic.
        """
        await self._rate_limit()

        # Log input details
        code_lines = len(code.split('\n'))
        code_chars = len(code)
        logger.info(f"🚀 GEMINI REQUEST - Language: {language}, Lines: {code_lines}, Characters: {code_chars}")
        logger.debug(f"📝 GEMINI INPUT CODE:\n{'-'*50}\n{code}\n{'-'*50}")
        prompt = f"""
        Analyze this {language} code and generate meaningful inline comments that explain:
        - Complex logic and algorithms
        - Non-obvious implementation details
        - Important technical decisions

        Code:
        {code}

        IMPORTANT: You MUST format your response as a valid JSON object with a "comments" array like this:
        {{
            "comments": [
                {{"line_number": 10, "comment": "Initialize the database connection pool"}},
                {{"line_number": 15, "comment": "Execute the main query with retry logic"}}
            ]
        }}
        Ensure the entire response is ONLY this JSON object and nothing else.

        Guidelines:
        - Focus on explaining WHY, not WHAT
        - Only comment non-obvious code
        - Keep comments brief and technical
        - Skip obvious or self-documenting code
        """

        # Log the full prompt being sent
        prompt_chars = len(prompt)
        logger.info(f"📤 GEMINI PROMPT - Characters: {prompt_chars}")
        logger.debug(f"📤 GEMINI FULL PROMPT:\n{'-'*50}\n{prompt}\n{'-'*50}")

        for attempt in range(self.max_retries):
            try:
                # Use HTTP requests to call Gemini REST API
                headers = {
                    'Content-Type': 'application/json',
                }

                # Prepare the request payload
                payload = {
                    "contents": [{
                        "parts": [{
                            "text": prompt
                        }]
                    }],
                    "generationConfig": {
                        "temperature": 0.1,
                        "maxOutputTokens": 2048,
                    }
                }

                # Make the API call
                async with aiohttp.ClientSession() as session:
                    url_with_key = f"{self.api_url}?key={self.api_key}"
                    async with session.post(url_with_key, headers=headers, json=payload) as response:
                        if response.status == 200:
                            response_data = await response.json()

                            # Extract text from response
                            if 'candidates' in response_data and response_data['candidates']:
                                candidate = response_data['candidates'][0]
                                if 'content' in candidate and 'parts' in candidate['content']:
                                    response_text = candidate['content']['parts'][0].get('text', '')
                                else:
                                    logger.warning("No content found in Gemini response")
                                    if attempt == self.max_retries - 1:
                                        return []
                                    continue
                            else:
                                logger.warning("No candidates found in Gemini response")
                                if attempt == self.max_retries - 1:
                                    return []
                                continue
                        else:
                            error_text = await response.text()
                            logger.warning(f"Gemini API error {response.status}: {error_text}")
                            if attempt == self.max_retries - 1:
                                return []
                            continue

                # Log the raw response
                response_chars = len(response_text) if response_text else 0
                logger.info(f"📥 GEMINI RESPONSE - Characters: {response_chars}")
                logger.debug(f"📥 GEMINI RAW RESPONSE:\n{'-'*50}\n{response_text}\n{'-'*50}")

                # Try to parse the JSON response
                try:
                    # It's common for LLMs to wrap JSON in ```json ... ```, try to strip it.
                    if response_text.strip().startswith("```json"):
                        response_text = response_text.strip()[7:]
                        if response_text.strip().endswith("```"):
                            response_text = response_text.strip()[:-3]

                    parsed = json.loads(response_text.strip())

                    if isinstance(parsed, dict) and "comments" in parsed:
                        comments_list = parsed["comments"]
                        if isinstance(comments_list, list):
                            valid_comments = []
                            for item in comments_list:
                                if (isinstance(item, dict) and
                                        "line_number" in item and isinstance(item["line_number"], int) and
                                        "comment" in item and isinstance(item["comment"], str)):
                                    valid_comments.append(item)
                                else:
                                    logger.warning(f"Invalid comment item format or type in Gemini response: {item}")
                            logger.info(f"✅ GEMINI SUCCESS - Generated {len(valid_comments)} valid comments")
                            logger.debug(f"✅ GEMINI PARSED COMMENTS:\n{json.dumps(valid_comments, indent=2)}")
                            return valid_comments
                        else:
                            logger.warning(f"'comments' key in Gemini JSON is not a list: {parsed.get('comments')}")
                            if attempt == self.max_retries - 1:
                                return []
                            continue
                    else:
                        logger.warning(f"Unexpected JSON structure or 'comments' key missing in Gemini response: {parsed}")
                        if attempt == self.max_retries - 1:
                            return []
                        continue

                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON response from Gemini: {e}")
                    logger.debug(f"Raw response text from Gemini for JSON parsing: '{response_text}'")
                    if attempt == self.max_retries - 1:
                        return []
                    continue

            except Exception as e:
                logger.warning(f"Gemini attempt {attempt + 1}/{self.max_retries} failed: {e}")
                if attempt == self.max_retries - 1:
                    logger.error(f"All {self.max_retries} Gemini attempts failed")
                    return []

                # Exponential backoff for retries
                wait_time = 2 ** attempt
                logger.info(f"Retrying Gemini in {wait_time} seconds...")
                await asyncio.sleep(wait_time)

        return []
