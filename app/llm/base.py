from abc import ABC, abstractmethod
from typing import List, Dict, Type, Any
import os
import logging
from app.utils.language_detector import detect_language

logger = logging.getLogger(__name__)

class BaseDocGenerator(ABC):
    """
    Abstract base class for all LLM document generators.
    """
    @abstractmethod
    async def generate_inline_comments(self, code: str, language: str, file_path: str = "") -> List[Dict[str, Any]]:
        """
        Generate Doxygen-compatible comments for code blocks.
        Returns a list of dicts with 'line_number', 'comment', and 'type'.
        """
        code_length = len(code.encode('utf-8'))
        logger.info(f"LLM: {self.__class__.__name__} - Sending {code_length} bytes for language: {language}")
        logger.debug(f"LLM: {self.__class__.__name__} - Full code content:\n{code}")
        return []

    def _detect_language(self, file_path: str) -> str:
        """
        Detect the programming language using the utility function.
        """
        return detect_language(file_path)

class LLMFactory:
    """
    Factory for creating LLM document generators.
    """
    _registry = {}

    @classmethod
    def register(cls, name: str, generator_class: Type[BaseDocGenerator]) -> None:
        """
        Register a new LLM generator class.
        """
        cls._registry[name] = generator_class
        logger.info(f"Registered LLM generator: {name}")

    @classmethod
    def create(cls, provider: str = None) -> BaseDocGenerator:
        """
        Create an instance of the specified LLM generator.
        """
        if provider is None:
            provider = cls._determine_provider_from_env()
        if provider not in cls._registry:
            available = ', '.join(cls._registry.keys())
            raise ValueError(f"LLM provider '{provider}' not registered. Available: {available}")
        return cls._registry[provider]()

    @classmethod
    def _determine_provider_from_env(cls) -> str:
        """
        Determine the LLM provider based on environment variables.
        """
        if os.getenv('GROQ_API_KEY'):
            return 'groq'
        if os.getenv('OPENAI_API_KEY'):
            return 'openai'
        if os.getenv('GOOGLE_GEMINI_API_KEY'):
            return 'gemini'
        raise ValueError("No API keys found for any supported LLM provider")

    @classmethod
    def list_available_providers(cls) -> List[str]:
        """
        List all registered LLM providers.
        """
        return list(cls._registry.keys())
