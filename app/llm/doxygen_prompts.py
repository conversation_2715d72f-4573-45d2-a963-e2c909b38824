"""
Doxygen-compatible comment generation prompts and templates.
Supports multiple programming languages with appropriate Doxygen syntax.
"""

from typing import Dict, List
import re

class DoxygenPromptGenerator:
    """Generates language-specific Doxygen prompts and validates comment syntax."""
    
    # Language-specific comment syntax mapping
    COMMENT_SYNTAX = {
        'c++': {'block': '/**', 'line': ' * ', 'end': ' */'},
        'cpp': {'block': '/**', 'line': ' * ', 'end': ' */'},
        'c': {'block': '/**', 'line': ' * ', 'end': ' */'},
        'h': {'block': '/**', 'line': ' * ', 'end': ' */'},
        'hpp': {'block': '/**', 'line': ' * ', 'end': ' */'},
        'python': {'block': '##', 'line': '## ', 'end': ''},
        'py': {'block': '##', 'line': '## ', 'end': ''},
        'javascript': {'block': '/**', 'line': ' * ', 'end': ' */'},
        'js': {'block': '/**', 'line': ' * ', 'end': ' */'},
        'typescript': {'block': '/**', 'line': ' * ', 'end': ' */'},
        'ts': {'block': '/**', 'line': ' * ', 'end': ' */'},
        'java': {'block': '/**', 'line': ' * ', 'end': ' */'},
        'php': {'block': '/**', 'line': ' * ', 'end': ' */'},
        'go': {'block': '/**', 'line': ' * ', 'end': ' */'},
        'rust': {'block': '/**', 'line': ' * ', 'end': ' */'},
        'rs': {'block': '/**', 'line': ' * ', 'end': ' */'},
    }
    
    # Doxygen tags for different code elements
    DOXYGEN_TAGS = {
        'file': ['@file', '@brief', '@author', '@date', '@version', '@copyright'],
        'class': ['@class', '@brief', '@details', '@author', '@date'],
        'function': ['@brief', '@param', '@return', '@throws', '@details', '@note'],
        'variable': ['@brief', '@details'],
        'namespace': ['@namespace', '@brief', '@details'],
        'enum': ['@enum', '@brief', '@details'],
        'struct': ['@struct', '@brief', '@details'],
        'typedef': ['@typedef', '@brief', '@details']
    }

    @classmethod
    def get_comment_syntax(cls, language: str) -> Dict[str, str]:
        """Get comment syntax for the specified language."""
        lang_key = language.lower().replace('.', '')
        return cls.COMMENT_SYNTAX.get(lang_key, cls.COMMENT_SYNTAX['c++'])

    @classmethod
    def generate_doxygen_prompt(cls, code: str, language: str, file_path: str = "") -> str:
        """Generate a comprehensive Doxygen comment generation prompt."""
        
        syntax = cls.get_comment_syntax(language)
        file_name = file_path.split('/')[-1] if file_path else f"example.{language}"
        
        prompt = f"""
You are a professional software documentation expert. Analyze the following {language} code and generate comprehensive Doxygen-compatible comments.

CRITICAL REQUIREMENTS:
1. Generate comments in proper {language} Doxygen format using {syntax['block']} syntax
2. Include appropriate Doxygen tags (@brief, @param, @return, @file, @class, etc.)
3. Focus on documenting the PURPOSE and BEHAVIOR, not obvious implementation details
4. Use professional, technical language suitable for API documentation

COMMENT SYNTAX FOR {language.upper()}:
- Block comments: {syntax['block']} ... {syntax['end']}
- Line prefix: {syntax['line']}

DOXYGEN TAGS TO USE:
- @file: For file headers
- @brief: Brief description (required for all major elements)
- @param: Function/method parameters
- @return: Return value description
- @class: Class documentation
- @namespace: Namespace documentation
- @details: Detailed explanation when needed
- @note: Important notes or warnings
- @author: Author information (use "Generated by AI Documentation System")
- @date: Current date
- @version: Version information

CODE TO ANALYZE:
{code}

RESPONSE FORMAT:
You MUST respond with a valid JSON object containing a "comments" array. Each comment should specify:
- line_number: The line where the comment should be inserted (1-based)
- comment: The complete Doxygen comment block (including proper syntax)

Example response format:
{{
    "comments": [
        {{
            "line_number": 1,
            "comment": "{syntax['block']}\\n{syntax['line']}@file {file_name}\\n{syntax['line']}@brief Brief description of the file\\n{syntax['line']}<AUTHOR> by AI Documentation System\\n{syntax['line']}@date $(date)\\n{syntax['end']}"
        }},
        {{
            "line_number": 15,
            "comment": "{syntax['block']}\\n{syntax['line']}@brief Brief description of the function\\n{syntax['line']}@param param_name Description of parameter\\n{syntax['line']}@return Description of return value\\n{syntax['end']}"
        }}
    ]
}}

DOCUMENTATION GUIDELINES:
1. Add file header comment at the beginning (line 1)
2. Document all classes with @class and @brief
3. Document all public functions/methods with @brief, @param, @return
4. Document complex algorithms with @details
5. Add @note for important implementation details
6. Use consistent, professional terminology
7. Avoid redundant comments for self-explanatory code
8. Focus on API contract and behavior documentation

Generate comprehensive Doxygen documentation that would be suitable for professional API documentation.
"""
        return prompt

    @classmethod
    def validate_doxygen_comment(cls, comment: str, language: str) -> bool:
        """Validate that a comment follows Doxygen syntax for the given language."""
        syntax = cls.get_comment_syntax(language)
        
        # Check if comment starts with proper block comment syntax
        if not comment.strip().startswith(syntax['block']):
            return False
            
        # Check for at least one Doxygen tag
        doxygen_tags = ['@file', '@brief', '@param', '@return', '@class', '@namespace', '@details']
        has_doxygen_tag = any(tag in comment for tag in doxygen_tags)
        
        return has_doxygen_tag

    @classmethod
    def format_comment_for_language(cls, comment: str, language: str) -> str:
        """Ensure comment is properly formatted for the target language."""
        syntax = cls.get_comment_syntax(language)
        
        # If comment doesn't start with proper syntax, wrap it
        if not comment.strip().startswith(syntax['block']):
            lines = comment.strip().split('\n')
            formatted_lines = [syntax['block']]
            for line in lines:
                formatted_lines.append(f"{syntax['line']}{line}")
            if syntax['end']:
                formatted_lines.append(syntax['end'])
            return '\n'.join(formatted_lines)
        
        return comment

    @classmethod
    def get_language_examples(cls, language: str) -> Dict[str, str]:
        """Get example Doxygen comments for the specified language."""
        syntax = cls.get_comment_syntax(language)
        
        examples = {
            'file_header': f"""{syntax['block']}
{syntax['line']}@file example.{language}
{syntax['line']}@brief Brief description of the file purpose
{syntax['line']}<AUTHOR> by AI Documentation System
{syntax['line']}@date $(date)
{syntax['line']}@version 1.0
{syntax['end']}""",
            
            'function': f"""{syntax['block']}
{syntax['line']}@brief Brief description of the function
{syntax['line']}@param param1 Description of first parameter
{syntax['line']}@param param2 Description of second parameter
{syntax['line']}@return Description of return value
{syntax['line']}@details Detailed explanation if needed
{syntax['end']}""",
            
            'class': f"""{syntax['block']}
{syntax['line']}@class ClassName
{syntax['line']}@brief Brief description of the class
{syntax['line']}@details Detailed explanation of class purpose and usage
{syntax['end']}"""
        }
        
        return examples
